import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

class WebProfileService {
  static const String _profileDataKey = 'web_profile_data';
  static const String _profileStatsKey = 'web_profile_stats';

  // Get profile data from local storage (web-compatible)
  static Future<Map<String, dynamic>?> getProfileData(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileDataJson = prefs.getString('${_profileDataKey}_$userId');
      
      if (profileDataJson != null) {
        return json.decode(profileDataJson);
      }
      
      // Return default profile data if none exists
      return {
        'id': userId,
        'name': 'User',
        'age': null,
        'bio': null,
        'location': null,
        'profile_images': [],
        'interests': [],
        'languages': [],
        'gender': null,
        'looking_for': null,
        'relationship_type': null,
        'occupation': null,
        'education': null,
        'height': null,
        'is_verified': false,
        'coins': 50,
        'is_premium': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error getting profile data: $e');
      }
      return null;
    }
  }

  // Update profile data in local storage (web-compatible)
  static Future<bool> updateProfile({
    required String userId,
    String? name,
    int? age,
    String? bio,
    String? location,
    String? occupation,
    String? education,
    String? gender,
    String? lookingFor,
    String? relationshipType,
    int? height,
    List<String>? interests,
    List<String>? languages,
  }) async {
    try {
      // Get existing profile data
      final existingData = await getProfileData(userId) ?? {};
      
      // Update only the provided fields
      final updatedData = Map<String, dynamic>.from(existingData);
      
      if (name != null && name.isNotEmpty) updatedData['name'] = name;
      if (age != null) updatedData['age'] = age;
      if (bio != null) updatedData['bio'] = bio;
      if (location != null) updatedData['location'] = location;
      if (occupation != null) updatedData['occupation'] = occupation;
      if (education != null) updatedData['education'] = education;
      if (gender != null) updatedData['gender'] = gender;
      if (lookingFor != null) updatedData['looking_for'] = lookingFor;
      if (relationshipType != null) updatedData['relationship_type'] = relationshipType;
      if (height != null) updatedData['height'] = height;
      if (interests != null) updatedData['interests'] = interests;
      if (languages != null) updatedData['languages'] = languages;
      
      // Update timestamp
      updatedData['updated_at'] = DateTime.now().toIso8601String();
      
      // Save to local storage
      final prefs = await SharedPreferences.getInstance();
      final profileDataJson = json.encode(updatedData);
      await prefs.setString('${_profileDataKey}_$userId', profileDataJson);
      
      if (kDebugMode) {
        print('Profile updated successfully in local storage');
        print('Updated data: $updatedData');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error updating profile: $e');
      }
      return false;
    }
  }

  // Get profile stats (mock data for web)
  static Future<Map<String, int>> getProfileStats(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = prefs.getString('${_profileStatsKey}_$userId');
      
      if (statsJson != null) {
        final stats = json.decode(statsJson);
        return {
          'matches': stats['matches'] ?? 0,
          'likes': stats['likes'] ?? 0,
          'views': stats['views'] ?? 0,
          'super_likes': stats['super_likes'] ?? 0,
        };
      }
      
      // Return default stats
      return {
        'matches': 12,
        'likes': 45,
        'views': 128,
        'super_likes': 3,
      };
    } catch (e) {
      return {
        'matches': 0,
        'likes': 0,
        'views': 0,
        'super_likes': 0,
      };
    }
  }

  // Add profile image
  static Future<bool> addProfileImage(String userId, String imageUrl) async {
    try {
      final profileData = await getProfileData(userId);
      if (profileData != null) {
        List<String> currentImages = List<String>.from(profileData['profile_images'] ?? []);
        
        if (!currentImages.contains(imageUrl)) {
          currentImages.add(imageUrl);
          profileData['profile_images'] = currentImages;
          profileData['updated_at'] = DateTime.now().toIso8601String();
          
          final prefs = await SharedPreferences.getInstance();
          final profileDataJson = json.encode(profileData);
          await prefs.setString('${_profileDataKey}_$userId', profileDataJson);
        }
        
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error adding profile image: $e');
      }
      return false;
    }
  }

  // Remove profile image
  static Future<bool> removeProfileImage(String userId, String imageUrl) async {
    try {
      final profileData = await getProfileData(userId);
      if (profileData != null) {
        List<String> currentImages = List<String>.from(profileData['profile_images'] ?? []);
        
        currentImages.remove(imageUrl);
        profileData['profile_images'] = currentImages;
        profileData['updated_at'] = DateTime.now().toIso8601String();
        
        final prefs = await SharedPreferences.getInstance();
        final profileDataJson = json.encode(profileData);
        await prefs.setString('${_profileDataKey}_$userId', profileDataJson);
        
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error removing profile image: $e');
      }
      return false;
    }
  }

  // Calculate profile completion percentage
  static double calculateCompletionPercentage(Map<String, dynamic> profileData) {
    int completedFields = 0;
    int totalFields = 10;
    
    if (profileData['name']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['age'] != null) completedFields++;
    if (profileData['bio']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['location']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['occupation']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['education']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['gender']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['looking_for']?.toString().isNotEmpty == true) completedFields++;
    if ((profileData['interests'] as List?)?.isNotEmpty == true) completedFields++;
    if ((profileData['profile_images'] as List?)?.isNotEmpty == true) completedFields++;
    
    return (completedFields / totalFields) * 100;
  }

  // Clear all profile data (for testing)
  static Future<void> clearProfileData(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('${_profileDataKey}_$userId');
      await prefs.remove('${_profileStatsKey}_$userId');
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing profile data: $e');
      }
    }
  }

  // Test function
  static Future<void> testProfileUpdate() async {
    if (kDebugMode) {
      print('Testing web profile update...');
      
      const testUserId = '55f2a97c-caae-4866-8bb7-f902a38bb29e';
      
      final success = await updateProfile(
        userId: testUserId,
        name: 'Test User Updated',
        bio: 'This is a test bio update for web',
        location: 'Test City Web',
      );
      
      print('Web profile update test result: $success');
      
      if (success) {
        final profileData = await getProfileData(testUserId);
        print('Updated web profile data: $profileData');
      }
    }
  }
}
