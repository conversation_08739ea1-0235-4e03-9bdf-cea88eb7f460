name: friendy
description: "A beautiful dating app with Flirtzy UI theme"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # UI/UX
  google_fonts: ^6.1.0

  # Firebase
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.3
  firebase_storage: ^12.3.2
  firebase_messaging: ^15.1.3
  google_sign_in: ^6.2.1

  # Database
  postgres: ^3.0.2

  # State Management
  provider: ^6.1.2

  # Utilities
  shared_preferences: ^2.3.2
  image_picker: ^1.1.2
  cached_network_image: ^3.4.1
  uuid: ^4.5.1
  intl: ^0.19.0
  crypto: ^3.0.5
  http: ^1.2.2
  dio: ^5.8.0+1
  pretty_dio_logger: ^1.4.0
  hive_flutter: ^1.1.0
  go_router: ^15.1.2
  url_launcher: ^6.3.1
  flutter_bloc: ^9.1.1
  equatable: ^2.0.7
  lottie: ^3.3.1
  shimmer: ^3.0.0
  flutter_card_swiper: ^7.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
