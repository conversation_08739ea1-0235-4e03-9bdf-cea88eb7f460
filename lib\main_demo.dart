import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'demo_app.dart';

void main() {
  runApp(const FriendyApp());
}

class FriendyApp extends StatelessWidget {
  const FriendyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Friendy - Dating App',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF2563EB),
          brightness: Brightness.dark, // Dark theme
        ),
        scaffoldBackgroundColor: const Color(0xFF1A1A2E), // Dark background
        textTheme: GoogleFonts.poppinsTextTheme().apply(
          bodyColor: Colors.white, // White text for dark theme
          displayColor: Colors.white,
        ),
        appBarTheme: const AppBarTheme(
          centerTitle: true,
          elevation: 0,
          backgroundColor: Colors.transparent,
        ),
      ),
      home: const DemoApp(),
      debugShowCheckedModeBanner: false,
    );
  }
}
