import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/providers/profile_provider.dart';
import '../../../../core/services/notification_service.dart';
import '../../../../core/constants/colors.dart';

class NotificationSettingsPage extends StatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  State<NotificationSettingsPage> createState() =>
      _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  bool _pushNotifications = true;
  bool _emailNotifications = false;
  bool _matchNotifications = true;
  bool _messageNotifications = true;
  bool _likeNotifications = true;
  bool _superLikeNotifications = true;
  bool _promotionalNotifications = false;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  String _quietHoursStart = '22:00';
  String _quietHoursEnd = '08:00';
  bool _quietHoursEnabled = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    final profileProvider =
        Provider.of<ProfileProvider>(context, listen: false);
    setState(() {
      _pushNotifications = profileProvider.pushNotifications;
      _emailNotifications = profileProvider.emailNotifications;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundDark,
      appBar: AppBar(
        title: const Text(
          'Notification Settings',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.backgroundDark,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // General Notifications
            _buildSection(
              title: 'General Notifications',
              children: [
                _buildSwitchTile(
                  title: 'Push Notifications',
                  subtitle: 'Receive notifications on your device',
                  value: _pushNotifications,
                  onChanged: (value) async {
                    setState(() {
                      _pushNotifications = value;
                    });
                    await _updateNotificationSettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'Email Notifications',
                  subtitle: 'Receive updates via email',
                  value: _emailNotifications,
                  onChanged: (value) {
                    setState(() {
                      _emailNotifications = value;
                    });
                    _updateNotificationSettings();
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Dating Activity
            _buildSection(
              title: 'Dating Activity',
              children: [
                _buildSwitchTile(
                  title: 'New Matches',
                  subtitle: 'When someone likes you back',
                  value: _matchNotifications,
                  enabled: _pushNotifications,
                  onChanged: (value) {
                    setState(() {
                      _matchNotifications = value;
                    });
                  },
                ),
                _buildSwitchTile(
                  title: 'New Messages',
                  subtitle: 'When you receive a message',
                  value: _messageNotifications,
                  enabled: _pushNotifications,
                  onChanged: (value) {
                    setState(() {
                      _messageNotifications = value;
                    });
                  },
                ),
                _buildSwitchTile(
                  title: 'Likes',
                  subtitle: 'When someone likes your profile',
                  value: _likeNotifications,
                  enabled: _pushNotifications,
                  onChanged: (value) {
                    setState(() {
                      _likeNotifications = value;
                    });
                  },
                ),
                _buildSwitchTile(
                  title: 'Super Likes',
                  subtitle: 'When someone super likes you',
                  value: _superLikeNotifications,
                  enabled: _pushNotifications,
                  onChanged: (value) {
                    setState(() {
                      _superLikeNotifications = value;
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Sound & Vibration
            _buildSection(
              title: 'Sound & Vibration',
              children: [
                _buildSwitchTile(
                  title: 'Sound',
                  subtitle: 'Play notification sounds',
                  value: _soundEnabled,
                  enabled: _pushNotifications,
                  onChanged: (value) {
                    setState(() {
                      _soundEnabled = value;
                    });
                  },
                ),
                _buildSwitchTile(
                  title: 'Vibration',
                  subtitle: 'Vibrate for notifications',
                  value: _vibrationEnabled,
                  enabled: _pushNotifications,
                  onChanged: (value) {
                    setState(() {
                      _vibrationEnabled = value;
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Quiet Hours
            _buildSection(
              title: 'Quiet Hours',
              children: [
                _buildSwitchTile(
                  title: 'Enable Quiet Hours',
                  subtitle: 'Pause notifications during specific hours',
                  value: _quietHoursEnabled,
                  enabled: _pushNotifications,
                  onChanged: (value) {
                    setState(() {
                      _quietHoursEnabled = value;
                    });
                  },
                ),
                if (_quietHoursEnabled) ...[
                  _buildTimeTile(
                    title: 'Start Time',
                    time: _quietHoursStart,
                    onTap: () => _selectTime(true),
                  ),
                  _buildTimeTile(
                    title: 'End Time',
                    time: _quietHoursEnd,
                    onTap: () => _selectTime(false),
                  ),
                ],
              ],
            ),

            const SizedBox(height: 24),

            // Marketing
            _buildSection(
              title: 'Marketing',
              children: [
                _buildSwitchTile(
                  title: 'Promotional Notifications',
                  subtitle: 'Special offers and app updates',
                  value: _promotionalNotifications,
                  onChanged: (value) {
                    setState(() {
                      _promotionalNotifications = value;
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 32),

            // Test Notification Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _pushNotifications ? _sendTestNotification : null,
                icon: const Icon(Icons.notifications_active),
                label: const Text('Send Test Notification'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.cardDark,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    bool enabled = true,
  }) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          color: enabled ? Colors.white : Colors.grey,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: enabled ? Colors.white70 : Colors.grey,
          fontSize: 14,
        ),
      ),
      trailing: Switch(
        value: enabled ? value : false,
        onChanged: enabled ? onChanged : null,
        activeColor: const Color(0xFFFF6B9D),
      ),
    );
  }

  Widget _buildTimeTile({
    required String title,
    required String time,
    required VoidCallback onTap,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            time,
            style: const TextStyle(
              color: AppColors.primaryBlue,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          const Icon(
            Icons.chevron_right,
            color: Colors.white70,
          ),
        ],
      ),
      onTap: onTap,
    );
  }

  Future<void> _selectTime(bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: AppColors.primaryBlue,
              surface: AppColors.cardDark,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      final timeString =
          '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      setState(() {
        if (isStartTime) {
          _quietHoursStart = timeString;
        } else {
          _quietHoursEnd = timeString;
        }
      });
    }
  }

  Future<void> _updateNotificationSettings() async {
    final profileProvider =
        Provider.of<ProfileProvider>(context, listen: false);

    // Update profile provider
    await profileProvider.updateSettings({
      'push_notifications': _pushNotifications,
      'email_notifications': _emailNotifications,
    });

    // Update notification service
    if (_pushNotifications) {
      await NotificationService().initialize();
    }
  }

  void _sendTestNotification() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Test notification sent!'),
        backgroundColor: Color(0xFFFF6B9D),
      ),
    );
  }
}
