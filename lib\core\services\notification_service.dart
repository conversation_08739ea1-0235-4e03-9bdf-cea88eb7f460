import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  String? _fcmToken;

  // Initialize notification service
  Future<void> initialize() async {
    // Request permission for notifications
    await _requestPermission();

    // Get FCM token
    await _getFCMToken();

    // Configure message handlers
    _configureMessageHandlers();
  }

  // Request notification permission
  Future<void> _requestPermission() async {
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      debugPrint('User granted permission');
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      debugPrint('User granted provisional permission');
    } else {
      debugPrint('User declined or has not accepted permission');
    }
  }

  // Get FCM token
  Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();
      debugPrint('FCM Token: $_fcmToken');

      // Save token to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      if (_fcmToken != null) {
        await prefs.setString('fcm_token', _fcmToken!);
      }
    } catch (e) {
      debugPrint('Error getting FCM token: $e');
    }
  }

  // Get current FCM token
  String? get fcmToken => _fcmToken;

  // Configure message handlers
  void _configureMessageHandlers() {
    // Handle messages when app is in foreground
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('Got a message whilst in the foreground!');
      debugPrint('Message data: ${message.data}');

      if (message.notification != null) {
        debugPrint(
            'Message also contained a notification: ${message.notification}');
        _showInAppNotification(message);
      }
    });

    // Handle messages when app is opened from background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('A new onMessageOpenedApp event was published!');
      _handleMessageTap(message);
    });

    // Handle token refresh
    _firebaseMessaging.onTokenRefresh.listen((String token) {
      debugPrint('FCM Token refreshed: $token');
      _fcmToken = token;
      _saveFCMToken(token);
    });
  }

  // Save FCM token to SharedPreferences
  Future<void> _saveFCMToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('fcm_token', token);
  }

  // Show in-app notification
  void _showInAppNotification(RemoteMessage message) {
    // This would typically show a custom in-app notification
    // For now, we'll just print the message
    debugPrint('Showing in-app notification: ${message.notification?.title}');
  }

  // Handle notification tap
  void _handleMessageTap(RemoteMessage message) {
    debugPrint('Notification tapped: ${message.data}');

    // Navigate to appropriate screen based on message data
    final messageType = message.data['type'];
    switch (messageType) {
      case 'new_message':
        _navigateToChat(message.data);
        break;
      case 'new_match':
        _navigateToMatches();
        break;
      case 'profile_view':
        _navigateToProfile();
        break;
      default:
        _navigateToHome();
    }
  }

  // Navigation methods
  void _navigateToChat(Map<String, dynamic> data) {
    // TODO: Implement navigation to specific chat
    debugPrint('Navigate to chat: ${data['conversationId']}');
  }

  void _navigateToMatches() {
    // TODO: Implement navigation to matches
    debugPrint('Navigate to matches');
  }

  void _navigateToProfile() {
    // TODO: Implement navigation to profile
    debugPrint('Navigate to profile');
  }

  void _navigateToHome() {
    // TODO: Implement navigation to home
    debugPrint('Navigate to home');
  }

  // Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      debugPrint('Subscribed to topic: $topic');
    } catch (e) {
      debugPrint('Error subscribing to topic: $e');
    }
  }

  // Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      debugPrint('Unsubscribed from topic: $topic');
    } catch (e) {
      debugPrint('Error unsubscribing from topic: $e');
    }
  }

  // Send notification data to server
  Future<void> updateFCMTokenOnServer(String userId) async {
    if (_fcmToken == null) return;

    try {
      // TODO: Send FCM token to your backend server
      // This would typically be an API call to update user's FCM token
      debugPrint('Updating FCM token on server for user: $userId');
      debugPrint('Token: $_fcmToken');

      // Example API call structure:
      // await apiService.updateFCMToken(userId, _fcmToken!);
    } catch (e) {
      debugPrint('Error updating FCM token on server: $e');
    }
  }

  // Clear notification badge
  Future<void> clearBadge() async {
    try {
      await _firebaseMessaging.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: false,
        sound: true,
      );
    } catch (e) {
      debugPrint('Error clearing badge: $e');
    }
  }

  // Handle background messages
  static Future<void> handleBackgroundMessage(RemoteMessage message) async {
    debugPrint('Handling a background message: ${message.messageId}');
    debugPrint('Message data: ${message.data}');
    debugPrint('Message notification: ${message.notification?.title}');
  }
}

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await NotificationService.handleBackgroundMessage(message);
}
