@echo off
echo ========================================
echo    FRIENDY APP INSTALLATION SCRIPT
echo ========================================
echo.

echo [1/5] Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo ERROR: Flutter not found!
    pause
    exit /b 1
)

echo.
echo [2/5] Checking connected devices...
flutter devices
if %errorlevel% neq 0 (
    echo ERROR: No devices found!
    echo Please ensure your device is connected via USB or wireless debugging
    pause
    exit /b 1
)

echo.
echo [3/5] Cleaning previous builds...
flutter clean
flutter pub get

echo.
echo [4/5] Building optimized APK...
flutter build apk --debug --target-platform android-arm64

echo.
echo [5/5] Installing APK to device...
echo Attempting installation methods...

echo Method 1: Direct install...
flutter install --debug
if %errorlevel% equ 0 (
    echo SUCCESS: App installed successfully!
    goto :success
)

echo Method 1 failed, trying Method 2: ADB install...
adb install -r build\app\outputs\flutter-apk\app-debug.apk
if %errorlevel% equ 0 (
    echo SUCCESS: App installed successfully!
    goto :success
)

echo Method 2 failed, trying Method 3: Force install...
adb uninstall com.friendy.friendy
adb install build\app\outputs\flutter-apk\app-debug.apk
if %errorlevel% equ 0 (
    echo SUCCESS: App installed successfully!
    goto :success
)

echo.
echo ========================================
echo   INSTALLATION FAILED - MANUAL STEPS
echo ========================================
echo.
echo The APK has been built successfully at:
echo build\app\outputs\flutter-apk\app-debug.apk
echo.
echo Please install manually:
echo 1. Copy the APK file to your phone
echo 2. Enable "Install from Unknown Sources"
echo 3. Tap the APK file to install
echo.
goto :end

:success
echo.
echo ========================================
echo     FRIENDY APP INSTALLED SUCCESSFULLY!
echo ========================================
echo.
echo The app should now be available on your device.
echo Look for the "Friendy" app icon.
echo.

:end
echo Press any key to exit...
pause >nul
