import 'dart:math';
import '../models/profile_model.dart';

class DiscoveryService {
  static final DiscoveryService _instance = DiscoveryService._internal();
  factory DiscoveryService() => _instance;
  DiscoveryService._internal();

  // Mock data for demo purposes
  final List<Map<String, dynamic>> _mockProfiles = [
    {
      'id': 'profile_1',
      'name': '<PERSON>',
      'age': 25,
      'bio': 'Love hiking, coffee, and good conversations. Looking for someone genuine!',
      'location': 'New York, NY',
      'profile_images': [
        'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=600&fit=crop',
        'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&h=600&fit=crop',
      ],
      'interests': ['Hiking', 'Coffee', 'Photography', 'Travel', 'Books'],
      'gender': 'female',
      'looking_for': 'male',
      'is_premium': false,
      'is_verified': true,
      'occupation': 'Graphic Designer',
      'education': 'Bachelor\'s Degree',
      'distance': 2.5,
    },
    {
      'id': 'profile_2',
      'name': '<PERSON>',
      'age': 28,
      'bio': 'Entrepreneur by day, chef by night. Let\'s cook something amazing together!',
      'location': 'Brooklyn, NY',
      'profile_images': [
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=600&fit=crop',
        'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=600&fit=crop',
      ],
      'interests': ['Cooking', 'Business', 'Fitness', 'Wine', 'Travel'],
      'gender': 'male',
      'looking_for': 'female',
      'is_premium': true,
      'is_verified': true,
      'occupation': 'Entrepreneur',
      'education': 'MBA',
      'distance': 5.2,
    },
    {
      'id': 'profile_3',
      'name': 'Sofia',
      'age': 23,
      'bio': 'Artist, dreamer, and dog lover. Life is too short for boring conversations!',
      'location': 'Manhattan, NY',
      'profile_images': [
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=600&fit=crop',
        'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=600&fit=crop',
      ],
      'interests': ['Art', 'Dogs', 'Music', 'Yoga', 'Nature'],
      'gender': 'female',
      'looking_for': 'male',
      'is_premium': false,
      'is_verified': false,
      'occupation': 'Artist',
      'education': 'Art School',
      'distance': 1.8,
    },
    {
      'id': 'profile_4',
      'name': 'Marcus',
      'age': 30,
      'bio': 'Software engineer who loves rock climbing and craft beer. Always up for an adventure!',
      'location': 'Queens, NY',
      'profile_images': [
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=600&fit=crop',
        'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=400&h=600&fit=crop',
      ],
      'interests': ['Rock Climbing', 'Beer', 'Technology', 'Gaming', 'Hiking'],
      'gender': 'male',
      'looking_for': 'female',
      'is_premium': false,
      'is_verified': true,
      'occupation': 'Software Engineer',
      'education': 'Computer Science Degree',
      'distance': 8.1,
    },
    {
      'id': 'profile_5',
      'name': 'Luna',
      'age': 26,
      'bio': 'Yoga instructor and wellness enthusiast. Seeking mindful connections and good vibes.',
      'location': 'Brooklyn, NY',
      'profile_images': [
        'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=600&fit=crop',
        'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=400&h=600&fit=crop',
      ],
      'interests': ['Yoga', 'Meditation', 'Wellness', 'Organic Food', 'Spirituality'],
      'gender': 'female',
      'looking_for': 'male',
      'is_premium': true,
      'is_verified': true,
      'occupation': 'Yoga Instructor',
      'education': 'Wellness Certification',
      'distance': 3.7,
    },
    {
      'id': 'profile_6',
      'name': 'David',
      'age': 27,
      'bio': 'Musician and music producer. Let\'s create beautiful melodies together!',
      'location': 'Manhattan, NY',
      'profile_images': [
        'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=400&h=600&fit=crop',
        'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=400&h=600&fit=crop',
      ],
      'interests': ['Music', 'Guitar', 'Recording', 'Concerts', 'Creativity'],
      'gender': 'male',
      'looking_for': 'female',
      'is_premium': false,
      'is_verified': false,
      'occupation': 'Musician',
      'education': 'Music School',
      'distance': 4.3,
    },
  ];

  // Track user interactions
  final Map<String, Set<String>> _userSwipes = {};
  final Map<String, Set<String>> _userLikes = {};
  final Map<String, Set<String>> _userMatches = {};

  Future<List<ProfileModel>> getDiscoveryProfiles(String currentUserId) async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Get profiles that haven't been swiped on yet
    final swipedProfiles = _userSwipes[currentUserId] ?? {};
    
    final availableProfiles = _mockProfiles
        .where((profile) => 
            profile['id'] != currentUserId && 
            !swipedProfiles.contains(profile['id']))
        .toList();

    // Shuffle for variety
    availableProfiles.shuffle(Random());

    // Convert to ProfileModel and add some randomization
    final profiles = availableProfiles.take(10).map((profileData) {
      // Add some random elements
      final randomizedData = Map<String, dynamic>.from(profileData);
      
      // Random last active time
      final now = DateTime.now();
      final randomMinutes = Random().nextInt(1440); // 0-24 hours
      randomizedData['last_active'] = now.subtract(Duration(minutes: randomMinutes)).toIso8601String();
      
      // Random distance variation
      if (randomizedData['distance'] != null) {
        final baseDistance = randomizedData['distance'] as double;
        final variation = (Random().nextDouble() - 0.5) * 2; // -1 to +1
        randomizedData['distance'] = (baseDistance + variation).clamp(0.1, 50.0);
      }

      return ProfileModel.fromMap(randomizedData);
    }).toList();

    return profiles;
  }

  Future<void> recordSwipe(String currentUserId, String profileId, bool isLike) async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 200));

    // Record the swipe
    _userSwipes.putIfAbsent(currentUserId, () => {}).add(profileId);

    if (isLike) {
      _userLikes.putIfAbsent(currentUserId, () => {}).add(profileId);
    }
  }

  Future<bool> checkForMatch(String currentUserId, String profileId) async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 300));

    // Check if the other user has also liked this user
    final otherUserLikes = _userLikes[profileId] ?? {};
    final isMatch = otherUserLikes.contains(currentUserId);

    if (isMatch) {
      // Record the match for both users
      _userMatches.putIfAbsent(currentUserId, () => {}).add(profileId);
      _userMatches.putIfAbsent(profileId, () => {}).add(currentUserId);
    }

    return isMatch;
  }

  Future<List<ProfileModel>> getMatches(String currentUserId) async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 400));

    final matchIds = _userMatches[currentUserId] ?? {};
    
    final matchProfiles = _mockProfiles
        .where((profile) => matchIds.contains(profile['id']))
        .map((profileData) => ProfileModel.fromMap(profileData))
        .toList();

    return matchProfiles;
  }

  Future<List<ProfileModel>> getLikes(String currentUserId) async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 300));

    final likeIds = _userLikes[currentUserId] ?? {};
    
    final likedProfiles = _mockProfiles
        .where((profile) => likeIds.contains(profile['id']))
        .map((profileData) => ProfileModel.fromMap(profileData))
        .toList();

    return likedProfiles;
  }

  Future<ProfileModel?> getProfile(String profileId) async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 200));

    final profileData = _mockProfiles.firstWhere(
      (profile) => profile['id'] == profileId,
      orElse: () => {},
    );

    if (profileData.isEmpty) return null;

    return ProfileModel.fromMap(profileData);
  }

  Future<void> superLike(String currentUserId, String profileId) async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 300));

    // Record as both a swipe and a like
    await recordSwipe(currentUserId, profileId, true);
    
    // Super likes have higher match probability (simulate by adding to other user's likes sometimes)
    if (Random().nextBool()) {
      _userLikes.putIfAbsent(profileId, () => {}).add(currentUserId);
    }
  }

  Future<void> undoLastSwipe(String currentUserId) async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 200));

    final userSwipes = _userSwipes[currentUserId];
    final userLikes = _userLikes[currentUserId];

    if (userSwipes != null && userSwipes.isNotEmpty) {
      final lastSwipe = userSwipes.last;
      userSwipes.remove(lastSwipe);
      userLikes?.remove(lastSwipe);
    }
  }

  // Analytics and stats
  Future<Map<String, dynamic>> getUserStats(String currentUserId) async {
    await Future.delayed(const Duration(milliseconds: 200));

    final swipes = _userSwipes[currentUserId]?.length ?? 0;
    final likes = _userLikes[currentUserId]?.length ?? 0;
    final matches = _userMatches[currentUserId]?.length ?? 0;

    return {
      'total_swipes': swipes,
      'total_likes': likes,
      'total_matches': matches,
      'like_rate': swipes > 0 ? (likes / swipes * 100).round() : 0,
      'match_rate': likes > 0 ? (matches / likes * 100).round() : 0,
    };
  }

  // Clear data (for testing)
  void clearUserData(String currentUserId) {
    _userSwipes.remove(currentUserId);
    _userLikes.remove(currentUserId);
    _userMatches.remove(currentUserId);
  }

  void clearAllData() {
    _userSwipes.clear();
    _userLikes.clear();
    _userMatches.clear();
  }
}
