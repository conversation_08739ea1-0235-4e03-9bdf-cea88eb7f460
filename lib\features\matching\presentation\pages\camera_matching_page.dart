import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:camera/camera.dart';
import 'package:permission_handler/permission_handler.dart';

class CameraMatchingPage extends StatefulWidget {
  const CameraMatchingPage({super.key});

  @override
  State<CameraMatchingPage> createState() => _CameraMatchingPageState();
}

class _CameraMatchingPageState extends State<CameraMatchingPage> {
  String _selectedGender = 'Female';
  bool _isMatching = false;
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;
  bool _isCameraInitialized = false;
  bool _isPermissionGranted = false;
  int _selectedCameraIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    super.dispose();
  }

  Future<void> _initializeCamera() async {
    try {
      debugPrint('🎥 Starting camera initialization...');

      // Check current permission status first
      var status = await Permission.camera.status;
      debugPrint('🎥 Current camera permission status: $status');

      if (!status.isGranted) {
        debugPrint('🎥 Requesting camera permission...');
        status = await Permission.camera.request();
        debugPrint('🎥 Permission request result: $status');
      }

      if (status.isGranted) {
        setState(() {
          _isPermissionGranted = true;
        });

        debugPrint('🎥 Getting available cameras...');
        _cameras = await availableCameras();
        debugPrint('🎥 Found ${_cameras?.length ?? 0} cameras');

        if (_cameras != null && _cameras!.isNotEmpty) {
          debugPrint(
              '🎥 Setting up camera: ${_cameras![_selectedCameraIndex].name}');
          await _setupCamera(_cameras![_selectedCameraIndex]);
        } else {
          debugPrint('🎥 No cameras available');
        }
      } else {
        debugPrint('🎥 Camera permission denied: $status');
        setState(() {
          _isPermissionGranted = false;
        });
      }
    } catch (e) {
      debugPrint('🎥 Error during camera initialization: $e');
      setState(() {
        _isPermissionGranted = false;
      });
    }
  }

  Future<void> _setupCamera(CameraDescription camera) async {
    try {
      debugPrint('🎥 Creating camera controller...');
      _cameraController = CameraController(
        camera,
        ResolutionPreset.medium, // Changed to medium for better compatibility
        enableAudio: false,
      );

      debugPrint('🎥 Initializing camera controller...');
      await _cameraController!.initialize();

      if (mounted) {
        debugPrint('🎥 Camera initialized successfully!');
        setState(() {
          _isCameraInitialized = true;
        });
      }
    } catch (e) {
      debugPrint('🎥 Error initializing camera: $e');
      if (mounted) {
        setState(() {
          _isCameraInitialized = false;
        });
      }
    }
  }

  Future<void> _switchCamera() async {
    if (_cameras == null || _cameras!.length < 2) return;

    setState(() {
      _isCameraInitialized = false;
    });

    await _cameraController?.dispose();

    _selectedCameraIndex = (_selectedCameraIndex + 1) % _cameras!.length;
    await _setupCamera(_cameras![_selectedCameraIndex]);
  }

  Widget _buildCameraLoadingView() {
    if (!_isPermissionGranted) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.camera_alt,
                color: Colors.white,
                size: 100,
              ),
              const SizedBox(height: 20),
              const Text(
                'Camera Permission Required',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              const Text(
                'Please grant camera permission to start matching',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 30),
              ElevatedButton(
                onPressed: _initializeCamera,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF6B9D),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
                child: const Text('Enable Camera'),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              color: Color(0xFFFF6B9D),
            ),
            const SizedBox(height: 20),
            const Text(
              'Initializing Camera...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 30),
            TextButton(
              onPressed: _initializeCamera,
              child: const Text(
                'Retry Camera Setup',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      body: _buildCameraView(),
    );
  }

  Widget _buildCameraView() {
    return Container(
      color: Colors.black,
      child: Stack(
        children: [
          // Camera Preview - Full screen
          if (_cameraController != null &&
              _cameraController!.value.isInitialized)
            Positioned.fill(
              child: AspectRatio(
                aspectRatio: _cameraController!.value.aspectRatio,
                child: CameraPreview(_cameraController!),
              ),
            )
          else
            _buildCameraLoadingView(),

          // Top Controls
          Positioned(
            top: 50,
            left: 20,
            right: 20,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                GestureDetector(
                  onTap: _showGenderSelection,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: const Color(0xFFFF6B9D),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getGenderIcon(_selectedGender),
                          color: const Color(0xFFFF6B9D),
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Looking for $_selectedGender',
                          style: GoogleFonts.poppins(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 4),
                        const Icon(
                          Icons.keyboard_arrow_down,
                          color: Colors.white,
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                ),
                IconButton(
                  onPressed: _switchCamera,
                  icon: const Icon(
                    Icons.cameraswitch,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
              ],
            ),
          ),

          // Bottom Controls
          Positioned(
            bottom: 50,
            left: 20,
            right: 20,
            child: _isMatching
                ? _buildMatchingIndicator()
                : _buildCameraControls(),
          ),
        ],
      ),
    );
  }

  Widget _buildMatchingIndicator() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          const CircularProgressIndicator(
            color: Color(0xFFFF6B9D),
          ),
          const SizedBox(height: 16),
          Text(
            'Finding your perfect match...',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCameraControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // End Call
        Container(
          width: 60,
          height: 60,
          decoration: const BoxDecoration(
            color: Colors.red,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(
              Icons.call_end,
              color: Colors.white,
              size: 30,
            ),
          ),
        ),

        // Start Matching
        Container(
          width: 80,
          height: 80,
          decoration: const BoxDecoration(
            color: Color(0xFFFF6B9D),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: _findMatch,
            icon: const Icon(
              Icons.search,
              color: Colors.white,
              size: 40,
            ),
          ),
        ),

        // Settings
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: () {},
            icon: const Icon(
              Icons.settings,
              color: Colors.white,
              size: 30,
            ),
          ),
        ),
      ],
    );
  }

  void _findMatch() {
    setState(() {
      _isMatching = true;
    });

    // Simulate finding a match
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isMatching = false;
        });
        _showMatchDialog();
      }
    });
  }

  void _showMatchDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => MatchFoundDialog(
        onTextChat: () {
          Navigator.of(context).pop();
          // Navigate to chat
        },
        onVideoCall: () {
          Navigator.of(context).pop();
          // Start video call
        },
        onContinueMatching: () {
          Navigator.of(context).pop();
          _findMatch();
        },
      ),
    );
  }

  void _showGenderSelection() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Color(0xFF2A2A2A),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Select Gender Preference',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            _buildGenderOption('Female', Icons.female),
            const SizedBox(height: 12),
            _buildGenderOption('Male', Icons.male),
            const SizedBox(height: 12),
            _buildGenderOption('Any', Icons.people),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildGenderOption(String gender, IconData icon) {
    final isSelected = _selectedGender == gender;
    return GestureDetector(
      onTap: () {
        setState(() => _selectedGender = gender);
        Navigator.pop(context);
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFF6B9D) : const Color(0xFF1A1A1A),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? const Color(0xFFFF6B9D)
                : Colors.white.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
            const SizedBox(width: 16),
            Text(
              gender,
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            if (isSelected)
              const Icon(
                Icons.check,
                color: Colors.white,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  IconData _getGenderIcon(String gender) {
    switch (gender) {
      case 'Female':
        return Icons.female;
      case 'Male':
        return Icons.male;
      case 'Any':
        return Icons.people;
      default:
        return Icons.people;
    }
  }
}

class MatchFoundDialog extends StatelessWidget {
  final VoidCallback onTextChat;
  final VoidCallback onVideoCall;
  final VoidCallback onContinueMatching;

  const MatchFoundDialog({
    super.key,
    required this.onTextChat,
    required this.onVideoCall,
    required this.onContinueMatching,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A1A),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: const Color(0xFFFF6B9D).withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Profile Image
            Container(
              width: 100,
              height: 100,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                ),
              ),
              child: const Icon(
                Icons.person,
                color: Colors.white,
                size: 50,
              ),
            ),

            const SizedBox(height: 16),

            Text(
              'Match Found!',
              style: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              'Sarah, 24',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: const Color(0xFFFF6B9D),
              ),
            ),

            const SizedBox(height: 4),

            Text(
              'Online now',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.green,
              ),
            ),

            const SizedBox(height: 24),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: onTextChat,
                    icon: const Icon(Icons.chat, size: 20),
                    label: const Text('Text'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF9C27B0),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: onVideoCall,
                    icon: const Icon(Icons.videocam, size: 20),
                    label: const Text('Video'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFF6B9D),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: onContinueMatching,
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: Color(0xFFFF6B9D)),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'Continue Matching',
                  style: GoogleFonts.poppins(
                    color: const Color(0xFFFF6B9D),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
