import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

class SimpleCameraTest extends StatefulWidget {
  const SimpleCameraTest({super.key});

  @override
  State<SimpleCameraTest> createState() => _SimpleCameraTestState();
}

class _SimpleCameraTestState extends State<SimpleCameraTest> {
  String _status = 'Ready to test camera';
  bool _isLoading = false;

  Future<void> _testCameraPermission() async {
    setState(() {
      _isLoading = true;
      _status = 'Checking camera permission...';
    });

    try {
      final status = await Permission.camera.status;
      debugPrint('Camera permission status: $status');
      
      if (status.isDenied) {
        setState(() {
          _status = 'Requesting camera permission...';
        });
        
        final result = await Permission.camera.request();
        debugPrint('Permission request result: $result');
        
        setState(() {
          _status = 'Permission result: $result';
        });
      } else {
        setState(() {
          _status = 'Permission already granted: $status';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Error checking permission: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _openCamera() async {
    setState(() {
      _isLoading = true;
      _status = 'Opening camera...';
    });

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? photo = await picker.pickImage(
        source: ImageSource.camera,
        preferredCameraDevice: CameraDevice.front,
      );

      if (photo != null) {
        setState(() {
          _status = 'Photo captured successfully!\nPath: ${photo.path}';
        });
      } else {
        setState(() {
          _status = 'Camera cancelled by user';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Error opening camera: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('Camera Test'),
        backgroundColor: const Color(0xFFFF6B9D),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.camera_alt,
              color: Colors.white,
              size: 100,
            ),
            const SizedBox(height: 30),
            Text(
              _status,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),
            if (_isLoading)
              const CircularProgressIndicator(
                color: Color(0xFFFF6B9D),
              )
            else
              Column(
                children: [
                  ElevatedButton(
                    onPressed: _testCameraPermission,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFF6B9D),
                      foregroundColor: Colors.white,
                      minimumSize: const Size(200, 50),
                    ),
                    child: const Text('Test Permission'),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _openCamera,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(200, 50),
                    ),
                    child: const Text('Open Camera'),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
