import 'package:flutter/material.dart';

class CoinFeaturesCard extends StatelessWidget {
  const CoinFeaturesCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A2A),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'What can you do with coins?',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          _buildFeatureItem(
            icon: Icons.star,
            title: 'Super Like',
            description: 'Stand out and get 3x more matches',
            cost: '1 coin',
            color: const Color(0xFF4CAF50),
          ),
          
          _buildFeatureItem(
            icon: Icons.flash_on,
            title: 'Boost Profile',
            description: 'Be seen by more people for 30 minutes',
            cost: '5 coins',
            color: const Color(0xFF2196F3),
          ),
          
          _buildFeatureItem(
            icon: Icons.undo,
            title: 'Rewind',
            description: 'Undo your last swipe',
            cost: '1 coin',
            color: const Color(0xFF9C27B0),
          ),
          
          _buildFeatureItem(
            icon: Icons.filter_alt,
            title: 'Premium Filters',
            description: 'Filter by education, height, and more',
            cost: '2 coins',
            color: const Color(0xFFFF9800),
          ),
          
          _buildFeatureItem(
            icon: Icons.done_all,
            title: 'Read Receipts',
            description: 'See when your messages are read',
            cost: '1 coin',
            color: const Color(0xFF607D8B),
          ),
          
          _buildFeatureItem(
            icon: Icons.favorite_border,
            title: 'Unlimited Likes',
            description: 'Like as many profiles as you want for 24h',
            cost: '10 coins',
            color: const Color(0xFFE91E63),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
    required String cost,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          
          const SizedBox(width: 16),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: const Color(0xFFFFD700).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFFFFD700).withValues(alpha: 0.3),
              ),
            ),
            child: Text(
              cost,
              style: const TextStyle(
                color: Color(0xFFFFD700),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
