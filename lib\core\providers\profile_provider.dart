import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';

import '../services/profile_service.dart';

class ProfileProvider extends ChangeNotifier {
  final ProfileService _profileService = ProfileService();

  Map<String, dynamic>? _profileData;
  List<String> _profileImages = [];
  Map<String, int> _profileStats = {};
  Map<String, dynamic> _profileSettings = {};
  bool _isLoading = false;
  String? _error;
  double _completionPercentage = 0.0;
  String? _currentUserId;

  // Getters
  Map<String, dynamic>? get profileData => _profileData;
  List<String> get profileImages => _profileImages;
  Map<String, int> get profileStats => _profileStats;
  Map<String, dynamic> get profileSettings => _profileSettings;
  bool get isLoading => _isLoading;
  String? get error => _error;
  double get completionPercentage => _completionPercentage;

  // Profile data getters
  String get name => _profileData?['name'] ?? '';
  int? get age => _profileData?['age'];
  String get bio => _profileData?['bio'] ?? '';
  String get location => _profileData?['location'] ?? '';
  String get occupation => _profileData?['occupation'] ?? '';
  String get education => _profileData?['education'] ?? '';
  String get gender => _profileData?['gender'] ?? '';
  String get lookingFor => _profileData?['looking_for'] ?? '';
  int? get height => _profileData?['height'];
  List<String> get interests =>
      List<String>.from(_profileData?['interests'] ?? []);
  List<String> get languages =>
      List<String>.from(_profileData?['languages'] ?? []);
  String get relationshipType => _profileData?['relationship_type'] ?? '';
  bool get isVerified => _profileData?['is_verified'] ?? false;
  bool get isPremium => _profileData?['is_premium'] ?? false;

  // Stats getters
  int get matches => _profileStats['matches'] ?? 0;
  int get likes => _profileStats['likes'] ?? 0;
  int get views => _profileStats['views'] ?? 0;
  int get superLikes => _profileStats['super_likes'] ?? 0;

  // Settings getters
  bool get showOnlineStatus => _profileSettings['show_online_status'] ?? true;
  bool get showDistance => _profileSettings['show_distance'] ?? true;
  bool get showAge => _profileSettings['show_age'] ?? true;
  bool get pushNotifications => _profileSettings['push_notifications'] ?? true;
  bool get emailNotifications =>
      _profileSettings['email_notifications'] ?? false;
  double get maxDistance =>
      _profileSettings['max_distance']?.toDouble() ?? 50.0;
  int get ageRangeMin => _profileSettings['age_range_min'] ?? 18;
  int get ageRangeMax => _profileSettings['age_range_max'] ?? 35;

  ProfileProvider() {
    // Initialize with default data immediately - no loading
    _profileData = {
      'name': 'John Doe',
      'age': 25,
      'bio':
          'Love traveling, photography, and meeting new people. Looking for meaningful connections and fun adventures!',
      'location': 'New York, NY',
      'occupation': 'Software Developer',
      'education': 'Computer Science',
      'gender': 'Male',
      'looking_for': 'Serious Relationship',
      'height': 175,
      'interests': ['Travel', 'Photography', 'Music', 'Fitness', 'Cooking'],
      'languages': ['English', 'Spanish'],
      'relationship_type': 'Single',
      'profile_images': <String>[],
      'is_verified': true,
      'is_premium': false,
    };
    _profileImages = [];
    _profileStats = {
      'matches': 42,
      'likes': 128,
      'views': 256,
      'super_likes': 12,
    };
    _profileSettings = {
      'show_online_status': true,
      'show_distance': true,
      'show_age': true,
      'push_notifications': true,
      'email_notifications': false,
      'max_distance': 50.0,
      'age_range_min': 18,
      'age_range_max': 35,
    };
    _completionPercentage = 85.0;
    _isLoading = false; // Never loading
  }

  // Set current user ID for backend sync
  void setUserId(String? userId) {
    _currentUserId = userId;
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  // Load complete profile data
  Future<void> loadProfile() async {
    try {
      _setLoading(true);
      _clearError();

      // Load all profile data
      _profileData = await _profileService.getProfileData();
      _profileImages = await _profileService.getProfileImages();
      _profileStats = await _profileService.getProfileStats();
      _profileSettings = await _profileService.getProfileSettings();
      _completionPercentage =
          await _profileService.getProfileCompletionPercentage();

      notifyListeners();
    } catch (e) {
      _setError('Failed to load profile: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Update profile information
  Future<bool> updateProfile({
    String? name,
    int? age,
    String? bio,
    String? location,
    String? occupation,
    String? education,
    List<String>? interests,
    String? gender,
    String? lookingFor,
    int? height,
    List<String>? languages,
    String? relationshipType,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final success = await _profileService.updateProfile(
        name: name,
        age: age,
        bio: bio,
        location: location,
        occupation: occupation,
        education: education,
        interests: interests,
        gender: gender,
        lookingFor: lookingFor,
        height: height,
        languages: languages,
        relationshipType: relationshipType,
        userId: _currentUserId,
      );

      if (success) {
        await loadProfile(); // Reload to get updated data
        return true;
      } else {
        _setError('Failed to update profile');
        return false;
      }
    } catch (e) {
      _setError('Error updating profile: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Photo management
  Future<bool> addPhoto() async {
    try {
      _clearError();

      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1080,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image == null) return false;

      _setLoading(true);

      final success = await _profileService.addProfileImage(image);
      if (success) {
        await loadProfile(); // Reload to get updated images
        return true;
      } else {
        _setError('Failed to add photo. Maximum 6 photos allowed.');
        return false;
      }
    } catch (e) {
      _setError('Error adding photo: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> removePhoto(String imagePath) async {
    try {
      _setLoading(true);
      _clearError();

      final success = await _profileService.removeProfileImage(imagePath);
      if (success) {
        await loadProfile(); // Reload to get updated images
        return true;
      } else {
        _setError('Failed to remove photo');
        return false;
      }
    } catch (e) {
      _setError('Error removing photo: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> reorderPhotos(List<String> newOrder) async {
    try {
      _setLoading(true);
      _clearError();

      final success = await _profileService.reorderProfileImages(newOrder);
      if (success) {
        _profileImages = newOrder;
        notifyListeners();
        return true;
      } else {
        _setError('Failed to reorder photos');
        return false;
      }
    } catch (e) {
      _setError('Error reordering photos: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Settings management
  Future<bool> updateSetting(String key, dynamic value) async {
    try {
      final success = await _profileService.updateSetting(key, value);
      if (success) {
        _profileSettings[key] = value;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError('Error updating setting: ${e.toString()}');
      return false;
    }
  }

  Future<bool> updateSettings(Map<String, dynamic> settings) async {
    try {
      _setLoading(true);
      _clearError();

      final success = await _profileService.updateProfileSettings(settings);
      if (success) {
        _profileSettings = settings;
        notifyListeners();
        return true;
      } else {
        _setError('Failed to update settings');
        return false;
      }
    } catch (e) {
      _setError('Error updating settings: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Stats management
  Future<bool> incrementStat(String statName, {int increment = 1}) async {
    try {
      final success =
          await _profileService.incrementStat(statName, increment: increment);
      if (success) {
        _profileStats[statName] = (_profileStats[statName] ?? 0) + increment;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError('Error updating stats: ${e.toString()}');
      return false;
    }
  }

  // Profile validation
  Future<List<String>> getValidationErrors() async {
    return await _profileService.getProfileValidationErrors();
  }

  // Initialize profile for new user
  Future<bool> initializeProfile(
      String userId, String name, String email) async {
    try {
      _setLoading(true);
      _clearError();

      final success =
          await _profileService.initializeDefaultProfile(userId, name, email);
      if (success) {
        await loadProfile();
        return true;
      } else {
        _setError('Failed to initialize profile');
        return false;
      }
    } catch (e) {
      _setError('Error initializing profile: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Clear all profile data
  Future<bool> clearProfile() async {
    try {
      _setLoading(true);
      _clearError();

      final success = await _profileService.clearProfileData();
      if (success) {
        _profileData = null;
        _profileImages = [];
        _profileStats = {};
        _profileSettings = {};
        _completionPercentage = 0.0;
        notifyListeners();
        return true;
      } else {
        _setError('Failed to clear profile');
        return false;
      }
    } catch (e) {
      _setError('Error clearing profile: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Refresh profile data
  Future<void> refresh() async {
    await loadProfile();
  }
}
