class ProfileModel {
  final String id;
  final String name;
  final int? age;
  final String? bio;
  final String? location;
  final List<String> profileImages;
  final List<String> interests;
  final String? gender;
  final String? lookingFor;
  final bool isPremium;
  final DateTime? lastActive;
  final double? distance;
  final bool isVerified;
  final String? occupation;
  final String? education;
  final int? height;
  final List<String>? languages;
  final String? relationshipType;

  ProfileModel({
    required this.id,
    required this.name,
    this.age,
    this.bio,
    this.location,
    this.profileImages = const [],
    this.interests = const [],
    this.gender,
    this.lookingFor,
    this.isPremium = false,
    this.lastActive,
    this.distance,
    this.isVerified = false,
    this.occupation,
    this.education,
    this.height,
    this.languages,
    this.relationshipType,
  });

  factory ProfileModel.fromMap(Map<String, dynamic> map) {
    return ProfileModel(
      id: map['id']?.toString() ?? '',
      name: map['name']?.toString() ?? '',
      age: map['age'] is int
          ? map['age']
          : (map['age'] != null ? int.tryParse(map['age'].toString()) : null),
      bio: map['bio']?.toString(),
      location: map['location']?.toString(),
      profileImages: map['profile_images'] != null
          ? List<String>.from(map['profile_images'])
          : [],
      interests:
          map['interests'] != null ? List<String>.from(map['interests']) : [],
      gender: map['gender']?.toString(),
      lookingFor: map['looking_for']?.toString(),
      isPremium: map['is_premium'] == true || map['is_premium'] == 'true',
      lastActive: map['last_active'] != null
          ? DateTime.tryParse(map['last_active'].toString())
          : null,
      distance: map['distance'] is double
          ? map['distance']
          : (map['distance'] != null
              ? double.tryParse(map['distance'].toString())
              : null),
      isVerified: map['is_verified'] == true || map['is_verified'] == 'true',
      occupation: map['occupation']?.toString(),
      education: map['education']?.toString(),
      height: map['height'] is int
          ? map['height']
          : (map['height'] != null
              ? int.tryParse(map['height'].toString())
              : null),
      languages:
          map['languages'] != null ? List<String>.from(map['languages']) : null,
      relationshipType: map['relationship_type']?.toString(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'age': age,
      'bio': bio,
      'location': location,
      'profile_images': profileImages,
      'interests': interests,
      'gender': gender,
      'looking_for': lookingFor,
      'is_premium': isPremium,
      'last_active': lastActive?.toIso8601String(),
      'distance': distance,
      'is_verified': isVerified,
      'occupation': occupation,
      'education': education,
      'height': height,
      'languages': languages,
      'relationship_type': relationshipType,
    };
  }

  ProfileModel copyWith({
    String? id,
    String? name,
    int? age,
    String? bio,
    String? location,
    List<String>? profileImages,
    List<String>? interests,
    String? gender,
    String? lookingFor,
    bool? isPremium,
    DateTime? lastActive,
    double? distance,
    bool? isVerified,
    String? occupation,
    String? education,
    int? height,
    List<String>? languages,
    String? relationshipType,
  }) {
    return ProfileModel(
      id: id ?? this.id,
      name: name ?? this.name,
      age: age ?? this.age,
      bio: bio ?? this.bio,
      location: location ?? this.location,
      profileImages: profileImages ?? this.profileImages,
      interests: interests ?? this.interests,
      gender: gender ?? this.gender,
      lookingFor: lookingFor ?? this.lookingFor,
      isPremium: isPremium ?? this.isPremium,
      lastActive: lastActive ?? this.lastActive,
      distance: distance ?? this.distance,
      isVerified: isVerified ?? this.isVerified,
      occupation: occupation ?? this.occupation,
      education: education ?? this.education,
      height: height ?? this.height,
      languages: languages ?? this.languages,
      relationshipType: relationshipType ?? this.relationshipType,
    );
  }

  // Helper methods
  String get displayAge => age != null ? '$age' : '';

  String get displayLocation => location ?? '';

  String get displayDistance {
    if (distance == null) return '';
    if (distance! < 1) {
      return '${(distance! * 1000).round()}m away';
    } else {
      return '${distance!.round()}km away';
    }
  }

  String get primaryImage {
    return profileImages.isNotEmpty
        ? profileImages.first
        : 'https://via.placeholder.com/400x600/FF6B9D/FFFFFF?text=${name.isNotEmpty ? name[0].toUpperCase() : 'U'}';
  }

  List<String> get displayInterests {
    return interests.take(5).toList();
  }

  String get lastActiveText {
    if (lastActive == null) return 'Recently active';

    final now = DateTime.now();
    final difference = now.difference(lastActive!);

    if (difference.inMinutes < 60) {
      return 'Active ${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return 'Active ${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return 'Active ${difference.inDays}d ago';
    } else {
      return 'Active recently';
    }
  }

  bool get isOnline {
    if (lastActive == null) return false;
    final now = DateTime.now();
    return now.difference(lastActive!).inMinutes < 5;
  }

  // Compatibility score (mock calculation)
  int getCompatibilityScore(ProfileModel otherProfile) {
    int score = 50; // Base score

    // Age compatibility
    if (age != null && otherProfile.age != null) {
      final ageDiff = (age! - otherProfile.age!).abs();
      if (ageDiff <= 2) {
        score += 20;
      } else if (ageDiff <= 5)
        score += 10;
      else if (ageDiff > 10) score -= 10;
    }

    // Interest compatibility
    final commonInterests = interests
        .where((interest) => otherProfile.interests.contains(interest))
        .length;
    score += commonInterests * 5;

    // Location compatibility
    if (location == otherProfile.location) {
      score += 15;
    }

    // Education compatibility
    if (education != null && otherProfile.education != null) {
      if (education == otherProfile.education) {
        score += 10;
      }
    }

    return score.clamp(0, 100);
  }

  @override
  String toString() {
    return 'ProfileModel(id: $id, name: $name, age: $age, location: $location)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProfileModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
