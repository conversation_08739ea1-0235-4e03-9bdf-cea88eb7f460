import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/coin_package.dart';
import '../../../core/services/coin_service.dart';

class RechargeService {
  static const String _transactionHistoryKey = 'recharge_transactions';
  final CoinService _coinService = CoinService();

  // Get available coin packages
  Future<List<CoinPackage>> getCoinPackages() async {
    try {
      // For now, return default packages
      // In future, this can fetch from backend
      return CoinPackage.getDefaultPackages();
    } catch (e) {
      print('Error fetching coin packages: $e');
      return CoinPackage.getDefaultPackages();
    }
  }

  // Process coin purchase
  Future<PurchaseResult> purchaseCoins({
    required String userId,
    required CoinPackage package,
    required PaymentMethod paymentMethod,
    Map<String, String>? paymentDetails,
  }) async {
    try {
      // Simulate payment processing delay
      await Future.delayed(const Duration(seconds: 2));

      // For demo purposes, always succeed
      // In real app, integrate with actual payment gateway
      final success = await _processPayment(package, paymentMethod, paymentDetails);
      
      if (!success) {
        return PurchaseResult(
          success: false,
          errorMessage: 'Payment processing failed',
        );
      }

      // Add coins to user's balance
      final coinAdded = await _coinService.addCoins(
        package.totalCoins,
        'purchase',
        'Purchased ${package.name} - ${package.coins} coins + ${package.bonusCoins} bonus',
      );

      if (!coinAdded) {
        return PurchaseResult(
          success: false,
          errorMessage: 'Failed to add coins to wallet',
        );
      }

      // Get new balance
      final newBalance = await _coinService.getCoinBalance();

      // Record transaction
      await _recordTransaction(
        userId: userId,
        package: package,
        paymentMethod: paymentMethod,
        newBalance: newBalance,
      );

      return PurchaseResult(
        success: true,
        package: package,
        newBalance: newBalance,
        transactionId: 'TXN_${DateTime.now().millisecondsSinceEpoch}',
      );
    } catch (e) {
      print('Purchase error: $e');
      return PurchaseResult(
        success: false,
        errorMessage: 'An unexpected error occurred',
      );
    }
  }

  // Simulate payment processing
  Future<bool> _processPayment(
    CoinPackage package,
    PaymentMethod paymentMethod,
    Map<String, String>? paymentDetails,
  ) async {
    // Simulate payment gateway processing
    await Future.delayed(const Duration(milliseconds: 1500));
    
    // For demo, always return true
    // In real app, validate payment details and process with gateway
    return true;
  }

  // Record transaction in local storage
  Future<void> _recordTransaction({
    required String userId,
    required CoinPackage package,
    required PaymentMethod paymentMethod,
    required int newBalance,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final existingTransactions = prefs.getStringList(_transactionHistoryKey) ?? [];
      
      final transaction = {
        'id': 'TXN_${DateTime.now().millisecondsSinceEpoch}',
        'userId': userId,
        'packageId': package.id,
        'packageName': package.name,
        'coins': package.coins,
        'bonusCoins': package.bonusCoins,
        'totalCoins': package.totalCoins,
        'amount': package.priceINR,
        'paymentMethod': paymentMethod.name,
        'timestamp': DateTime.now().toIso8601String(),
        'status': 'completed',
        'newBalance': newBalance,
      };

      existingTransactions.add(jsonEncode(transaction));
      await prefs.setStringList(_transactionHistoryKey, existingTransactions);
    } catch (e) {
      print('Error recording transaction: $e');
    }
  }

  // Get transaction history
  Future<List<Map<String, dynamic>>> getTransactionHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final transactions = prefs.getStringList(_transactionHistoryKey) ?? [];
      
      return transactions
          .map((t) => jsonDecode(t) as Map<String, dynamic>)
          .toList()
          .reversed
          .toList();
    } catch (e) {
      print('Error fetching transaction history: $e');
      return [];
    }
  }
}

// Purchase result model
class PurchaseResult {
  final bool success;
  final CoinPackage? package;
  final int? newBalance;
  final String? transactionId;
  final String? errorMessage;

  PurchaseResult({
    required this.success,
    this.package,
    this.newBalance,
    this.transactionId,
    this.errorMessage,
  });
}

// Payment method enum
enum PaymentMethod {
  creditCard('Credit Card'),
  debitCard('Debit Card'),
  upi('UPI'),
  netBanking('Net Banking'),
  wallet('Wallet');

  const PaymentMethod(this.displayName);
  final String displayName;
}
