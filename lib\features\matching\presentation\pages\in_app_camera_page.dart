import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:permission_handler/permission_handler.dart';

class InAppCameraPage extends StatefulWidget {
  const InAppCameraPage({super.key});

  @override
  State<InAppCameraPage> createState() => _InAppCameraPageState();
}

class _InAppCameraPageState extends State<InAppCameraPage> {
  CameraController? _controller;
  List<CameraDescription>? _cameras;
  bool _isCameraInitialized = false;
  bool _hasPermission = false;
  String _status = 'Initializing...';
  int _selectedCameraIndex = 0;
  bool _isMatching = false;
  String _selectedGender = 'Female';

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  Future<void> _initializeCamera() async {
    try {
      setState(() {
        _status = 'Requesting camera permission...';
      });

      // Request camera permission
      final permission = await Permission.camera.request();

      if (permission.isGranted) {
        setState(() {
          _hasPermission = true;
          _status = 'Getting available cameras...';
        });

        // Get available cameras
        _cameras = await availableCameras();

        if (_cameras != null && _cameras!.isNotEmpty) {
          setState(() {
            _status = 'Setting up camera...';
          });

          await _setupCamera();
        } else {
          setState(() {
            _status = 'No cameras found on device';
          });
        }
      } else {
        setState(() {
          _hasPermission = false;
          _status = 'Camera permission denied';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  Future<void> _setupCamera() async {
    try {
      if (_cameras == null || _cameras!.isEmpty) return;

      _controller = CameraController(
        _cameras![_selectedCameraIndex],
        ResolutionPreset.medium,
        enableAudio: false,
      );

      await _controller!.initialize();

      if (mounted) {
        setState(() {
          _isCameraInitialized = true;
          _status = 'Camera ready!';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Camera setup failed: $e';
      });
    }
  }

  Future<void> _switchCamera() async {
    if (_cameras == null || _cameras!.length < 2) return;

    setState(() {
      _isCameraInitialized = false;
      _status = 'Switching camera...';
    });

    await _controller?.dispose();
    _selectedCameraIndex = (_selectedCameraIndex + 1) % _cameras!.length;
    await _setupCamera();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Camera Preview or Loading
            if (_isCameraInitialized && _controller != null)
              Positioned.fill(
                child: CameraPreview(_controller!),
              )
            else
              _buildLoadingView(),

            // Top Controls
            Positioned(
              top: 20,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Close Button
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),

                  // Status Text
                  GestureDetector(
                    onTap: _showGenderSelection,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: const Color(0xFFFF6B9D),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getGenderIcon(_selectedGender),
                            color: const Color(0xFFFF6B9D),
                            size: 14,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Looking for $_selectedGender',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Icon(
                            Icons.keyboard_arrow_down,
                            color: Colors.white,
                            size: 14,
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Switch Camera Button
                  IconButton(
                    onPressed: _isCameraInitialized ? _switchCamera : null,
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.5),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.cameraswitch,
                        color:
                            _isCameraInitialized ? Colors.white : Colors.grey,
                        size: 24,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Bottom Controls
            Positioned(
              bottom: 50,
              left: 20,
              right: 20,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // End Call Button
                  _buildControlButton(
                    icon: Icons.call_end,
                    color: Colors.red,
                    onPressed: () => Navigator.pop(context),
                  ),

                  // Start Matching Button
                  _buildMatchingButton(),

                  // Settings Button
                  _buildControlButton(
                    icon: Icons.settings,
                    color: Colors.white.withValues(alpha: 0.3),
                    onPressed: _showMatchSettings,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingView() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (!_hasPermission)
              const Icon(
                Icons.camera_alt,
                color: Colors.white,
                size: 80,
              )
            else
              const CircularProgressIndicator(
                color: Color(0xFFFF6B9D),
              ),
            const SizedBox(height: 20),
            Text(
              _status,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
              ),
              textAlign: TextAlign.center,
            ),
            if (!_hasPermission) ...[
              const SizedBox(height: 30),
              ElevatedButton(
                onPressed: _initializeCamera,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFF6B9D),
                  foregroundColor: Colors.white,
                ),
                child: const Text('Grant Camera Permission'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required Color color,
    required VoidCallback? onPressed,
    double size = 60,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          color: Colors.white,
          size: size * 0.4,
        ),
      ),
    );
  }

  Widget _buildMatchingButton() {
    return Container(
      width: 80,
      height: 80,
      decoration: const BoxDecoration(
        color: Color(0xFFFF6B9D),
        shape: BoxShape.circle,
      ),
      child: IconButton(
        onPressed: _isCameraInitialized && !_isMatching ? _startMatching : null,
        icon: _isMatching
            ? const SizedBox(
                width: 32,
                height: 32,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 3,
                ),
              )
            : const Icon(
                Icons.search,
                color: Colors.white,
                size: 32,
              ),
      ),
    );
  }

  void _startMatching() {
    if (!_isCameraInitialized || _isMatching) return;

    setState(() {
      _isMatching = true;
      _status = 'Searching for matches...';
    });

    // Simulate finding a match after 3 seconds
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isMatching = false;
          _status = 'Camera ready!';
        });
        _showMatchDialog();
      }
    });
  }

  void _showMatchDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2A2A2A),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.favorite,
              color: Color(0xFFFF6B9D),
              size: 60,
            ),
            const SizedBox(height: 20),
            const Text(
              'Match Found!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              'You have been matched with someone nearby!',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 30),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      // Navigate to text chat
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Opening text chat...'),
                          backgroundColor: Color(0xFFFF6B9D),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[700],
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Text Chat'),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      // Start video call
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Starting video call...'),
                          backgroundColor: Color(0xFFFF6B9D),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFF6B9D),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Video Call'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _startMatching(); // Continue searching
              },
              child: const Text(
                'Continue Matching',
                style: TextStyle(
                  color: Color(0xFFFF6B9D),
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showGenderSelection() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Color(0xFF2A2A2A),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Select Gender Preference',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            _buildGenderOption('Female', Icons.female),
            const SizedBox(height: 12),
            _buildGenderOption('Male', Icons.male),
            const SizedBox(height: 12),
            _buildGenderOption('Any', Icons.people),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildGenderOption(String gender, IconData icon) {
    final isSelected = _selectedGender == gender;
    return GestureDetector(
      onTap: () {
        setState(() => _selectedGender = gender);
        Navigator.pop(context);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFF6B9D) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFFFF6B9D) : Colors.grey[600]!,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Colors.grey[400],
              size: 24,
            ),
            const SizedBox(width: 12),
            Text(
              gender,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey[400],
                fontSize: 16,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getGenderIcon(String gender) {
    switch (gender) {
      case 'Female':
        return Icons.female;
      case 'Male':
        return Icons.male;
      case 'Any':
        return Icons.people;
      default:
        return Icons.people;
    }
  }

  void _showMatchSettings() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Color(0xFF2A2A2A),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Match Settings',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Camera Settings
            _buildSettingsSection(
              'Camera Settings',
              [
                _buildSettingsItem(
                  Icons.camera_alt,
                  'Camera Quality',
                  'Medium',
                  () => _showCameraQualityOptions(),
                ),
                _buildSettingsItem(
                  Icons.flash_on,
                  'Flash',
                  'Auto',
                  () => _showFlashOptions(),
                ),
                _buildSettingsItem(
                  Icons.cameraswitch,
                  'Default Camera',
                  'Front Camera',
                  () => _showDefaultCameraOptions(),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Matching Preferences
            _buildSettingsSection(
              'Matching Preferences',
              [
                _buildSettingsItem(
                  Icons.location_on,
                  'Search Radius',
                  '10 km',
                  () => _showRadiusOptions(),
                ),
                _buildSettingsItem(
                  Icons.cake,
                  'Age Range',
                  '18-35',
                  () => _showAgeRangeOptions(),
                ),
                _buildSettingsItem(
                  Icons.people,
                  'Gender Preference',
                  _selectedGender,
                  () => _showGenderSelection(),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Privacy & Safety
            _buildSettingsSection(
              'Privacy & Safety',
              [
                _buildSettingsItem(
                  Icons.block,
                  'Blocked Users',
                  'Manage',
                  () => _showBlockedUsers(),
                ),
                _buildSettingsItem(
                  Icons.report,
                  'Report Issues',
                  'Help',
                  () => _showReportOptions(),
                ),
                _buildSettingsItem(
                  Icons.visibility_off,
                  'Invisible Mode',
                  'Off',
                  () => _toggleInvisibleMode(),
                ),
              ],
            ),

            const SizedBox(height: 30),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsSection(String title, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Color(0xFFFF6B9D),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...items,
      ],
    );
  }

  Widget _buildSettingsItem(
    IconData icon,
    String title,
    String value,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey[700]!,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ),
            Text(
              value,
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.chevron_right,
              color: Colors.grey[400],
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  // Settings option methods
  void _showCameraQualityOptions() {
    Navigator.pop(context);
    _showOptionsDialog(
        'Camera Quality', ['Low', 'Medium', 'High', 'Ultra'], 'Medium');
  }

  void _showFlashOptions() {
    Navigator.pop(context);
    _showOptionsDialog('Flash Setting', ['Auto', 'On', 'Off'], 'Auto');
  }

  void _showDefaultCameraOptions() {
    Navigator.pop(context);
    _showOptionsDialog(
        'Default Camera', ['Front Camera', 'Back Camera'], 'Front Camera');
  }

  void _showRadiusOptions() {
    Navigator.pop(context);
    _showOptionsDialog('Search Radius',
        ['5 km', '10 km', '25 km', '50 km', '100 km'], '10 km');
  }

  void _showAgeRangeOptions() {
    Navigator.pop(context);
    _showOptionsDialog(
        'Age Range', ['18-25', '18-35', '25-40', '30-50', '18-60'], '18-35');
  }

  void _showBlockedUsers() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening blocked users list...'),
        backgroundColor: Color(0xFFFF6B9D),
      ),
    );
  }

  void _showReportOptions() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening report & help center...'),
        backgroundColor: Color(0xFFFF6B9D),
      ),
    );
  }

  void _toggleInvisibleMode() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Invisible mode toggled'),
        backgroundColor: Color(0xFFFF6B9D),
      ),
    );
  }

  void _showOptionsDialog(
      String title, List<String> options, String currentValue) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2A2A2A),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: options.map((option) {
            final isSelected = option == currentValue;
            return GestureDetector(
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('$title set to: $option'),
                    backgroundColor: const Color(0xFFFF6B9D),
                  ),
                );
              },
              child: Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color:
                      isSelected ? const Color(0xFFFF6B9D) : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected
                        ? const Color(0xFFFF6B9D)
                        : Colors.grey[600]!,
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    if (isSelected)
                      const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 20,
                      ),
                    if (isSelected) const SizedBox(width: 8),
                    Text(
                      option,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey[300],
                        fontSize: 16,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
