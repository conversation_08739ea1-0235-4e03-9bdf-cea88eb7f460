import 'package:flutter/material.dart';
import 'lib/features/matching/presentation/pages/beautiful_circular_match_page.dart';

void main() {
  runApp(const TestApp());
}

class TestApp extends StatelessWidget {
  const TestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Beautiful Circular Match Test',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFFFF6B9D),
          brightness: Brightness.dark,
        ),
      ),
      home: const BeautifulCircularMatchPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}
