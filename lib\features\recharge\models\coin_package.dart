class CoinPackage {
  final String id;
  final String name;
  final int coins;
  final int bonusCoins;
  final double priceINR;
  final bool isPopular;
  final String description;
  final String? badge;

  const CoinPackage({
    required this.id,
    required this.name,
    required this.coins,
    this.bonusCoins = 0,
    required this.priceINR,
    this.isPopular = false,
    required this.description,
    this.badge,
  });

  int get totalCoins => coins + bonusCoins;

  factory CoinPackage.fromJson(Map<String, dynamic> json) {
    return CoinPackage(
      id: json['id'] as String,
      name: json['name'] as String,
      coins: json['coins'] as int,
      bonusCoins: json['bonus_coins'] as int? ?? 0,
      priceINR: double.parse(json['price_inr'].toString()),
      isPopular: json['is_popular'] as bool? ?? false,
      description: json['description'] as String,
      badge: json['badge'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'coins': coins,
      'bonus_coins': bonusCoins,
      'price_inr': priceINR.toString(),
      'is_popular': isPopular,
      'description': description,
      'badge': badge,
    };
  }

  // Static method to get default packages
  static List<CoinPackage> getDefaultPackages() {
    return [
      const CoinPackage(
        id: 'starter',
        name: 'Starter Pack',
        coins: 100,
        bonusCoins: 0,
        priceINR: 79.0,
        isPopular: false,
        description: 'Perfect for trying out features',
      ),
      const CoinPackage(
        id: 'popular',
        name: 'Popular Pack',
        coins: 500,
        bonusCoins: 50,
        priceINR: 399.0,
        isPopular: true,
        description: 'Most popular choice',
        badge: 'BEST VALUE',
      ),
      const CoinPackage(
        id: 'value',
        name: 'Value Pack',
        coins: 1000,
        bonusCoins: 150,
        priceINR: 799.0,
        isPopular: false,
        description: 'Great value for money',
      ),
      const CoinPackage(
        id: 'premium',
        name: 'Premium Pack',
        coins: 2500,
        bonusCoins: 500,
        priceINR: 1599.0,
        isPopular: false,
        description: 'Maximum coins and bonus',
        badge: 'PREMIUM',
      ),
    ];
  }
}
