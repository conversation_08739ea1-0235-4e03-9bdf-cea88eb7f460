import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../core/providers/profile_provider.dart';

class ProfileInfoCard extends StatelessWidget {
  const ProfileInfoCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ProfileProvider>(
      builder: (context, profileProvider, child) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFF2A2A2A),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'About Me',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Bio
              if (profileProvider.bio.isNotEmpty)
                _buildInfoSection(
                  icon: Icons.info,
                  title: 'Bio',
                  content: profileProvider.bio,
                )
              else
                _buildEmptySection('Add a bio to tell others about yourself'),

              const SizedBox(height: 16),

              // Interests
              if (profileProvider.interests.isNotEmpty)
                _buildInterestsSection(profileProvider.interests)
              else
                _buildEmptySection('Add interests to find better matches'),

              const SizedBox(height: 16),

              // Basic Info
              _buildBasicInfo(profileProvider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoSection({
    required IconData icon,
    required String title,
    required String content,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: const Color(0xFFFF6B9D),
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          content,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 14,
            height: 1.4,
          ),
        ),
      ],
    );
  }

  Widget _buildInterestsSection(List<String> interests) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(
              Icons.favorite,
              color: Color(0xFFFF6B9D),
              size: 16,
            ),
            SizedBox(width: 8),
            Text(
              'Interests',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: interests.map<Widget>((interest) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFFFF6B9D).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: const Color(0xFFFF6B9D).withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                interest,
                style: const TextStyle(
                  color: Color(0xFFFF6B9D),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildBasicInfo(ProfileProvider profileProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(
              Icons.person,
              color: Color(0xFFFF6B9D),
              size: 16,
            ),
            SizedBox(width: 8),
            Text(
              'Basic Info',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Age
        if (profileProvider.age != null)
          _buildBasicInfoItem('Age', '${profileProvider.age} years old'),

        // Gender
        if (profileProvider.gender.isNotEmpty)
          _buildBasicInfoItem('Gender', profileProvider.gender),

        // Looking for
        if (profileProvider.lookingFor.isNotEmpty)
          _buildBasicInfoItem('Looking for', profileProvider.lookingFor),

        // Location
        if (profileProvider.location.isNotEmpty)
          _buildBasicInfoItem('Location', profileProvider.location),

        // Height
        if (profileProvider.height != null)
          _buildBasicInfoItem('Height', '${profileProvider.height} cm'),

        // Occupation
        if (profileProvider.occupation.isNotEmpty)
          _buildBasicInfoItem('Occupation', profileProvider.occupation),

        // Education
        if (profileProvider.education.isNotEmpty)
          _buildBasicInfoItem('Education', profileProvider.education),
      ],
    );
  }

  Widget _buildBasicInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptySection(String message) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.3),
          style: BorderStyle.solid,
        ),
      ),
      child: Text(
        message,
        style: const TextStyle(
          color: Colors.grey,
          fontSize: 12,
          fontStyle: FontStyle.italic,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
