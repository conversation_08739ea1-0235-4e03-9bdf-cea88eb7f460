import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math';

class HostLiveStreamingPage extends StatefulWidget {
  const HostLiveStreamingPage({super.key});

  @override
  State<HostLiveStreamingPage> createState() => _HostLiveStreamingPageState();
}

class _HostLiveStreamingPageState extends State<HostLiveStreamingPage>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _heartController;
  bool _isMuted = false;
  bool _isVideoOn = true;
  final bool _isLive = true;
  int _viewerCount = 0;
  double _earnings = 0.0;
  final List<String> _comments = [];
  final List<String> _viewers = [];
  final TextEditingController _responseController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _heartController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _generateRandomData();
    _simulateComments();
    _simulateViewers();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _heartController.dispose();
    _responseController.dispose();
    super.dispose();
  }

  void _generateRandomData() {
    setState(() {
      _viewerCount = 15 + Random().nextInt(50);
      _earnings = 50.0 + Random().nextDouble() * 100;
    });

    // Update data periodically
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted && _isLive) {
        _generateRandomData();
      }
    });
  }

  void _simulateComments() {
    final demoComments = [
      'You look amazing! 😍',
      'Hi from Delhi!',
      'Love your energy! ❤️',
      'Can you dance for us?',
      'You\'re so beautiful! 🔥',
      'What\'s your favorite song?',
      'Sending love from Mumbai! 💕',
      'You have a beautiful smile! 😊',
      'Can you say hi to me?',
      'Amazing live stream! 👏',
    ];

    Future.delayed(Duration(seconds: Random().nextInt(8) + 3), () {
      if (mounted && _isLive && _comments.length < 30) {
        setState(() {
          _comments.add(demoComments[Random().nextInt(demoComments.length)]);
          // Simulate earning from comments
          _earnings += Random().nextDouble() * 10;
        });
        _simulateComments();
      }
    });
  }

  void _simulateViewers() {
    final demoViewers = [
      'Sarah_123',
      'Mike_Cool',
      'Priya_Delhi',
      'Raj_Mumbai',
      'Anita_Pune',
      'Rohit_Bangalore',
      'Sneha_Chennai',
      'Amit_Kolkata',
      'Kavya_Hyderabad',
    ];

    Future.delayed(Duration(seconds: Random().nextInt(10) + 5), () {
      if (mounted && _isLive) {
        setState(() {
          if (_viewers.length < 8) {
            _viewers.add(demoViewers[Random().nextInt(demoViewers.length)]);
          }
          _viewerCount = _viewers.length + Random().nextInt(20);
        });
        _simulateViewers();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera Preview Background
          Positioned.fill(
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xFF9C27B0),
                    Color(0xFF673AB7),
                    Color(0xFF3F51B5),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: const Center(
                child: Icon(
                  Icons.person,
                  color: Colors.white,
                  size: 120,
                ),
              ),
            ),
          ),

          // Top Header
          Positioned(
            top: 50,
            left: 16,
            right: 16,
            child: Row(
              children: [
                // Live Indicator
                AnimatedBuilder(
                  animation: _pulseController,
                  builder: (context, child) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(
                          alpha: 0.7 + 0.3 * _pulseController.value,
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'LIVE',
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),

                const Spacer(),

                // Viewer Count
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.visibility,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        '$_viewerCount',
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 12),

                // Earnings
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.currency_rupee,
                        color: Colors.white,
                        size: 16,
                      ),
                      Text(
                        _earnings.toStringAsFixed(0),
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Active Viewers List
          Positioned(
            top: 100,
            right: 16,
            child: SizedBox(
              width: 60,
              height: 200,
              child: ListView.builder(
                itemCount: _viewers.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.primaries[index % Colors.primaries.length],
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: Center(
                      child: Text(
                        _viewers[index][0].toUpperCase(),
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // Comments Section
          Positioned(
            bottom: 120,
            left: 16,
            right: 80,
            child: SizedBox(
              height: 200,
              child: ListView.builder(
                reverse: true,
                itemCount: _comments.length,
                itemBuilder: (context, index) {
                  final comment = _comments[_comments.length - 1 - index];
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      comment,
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // Bottom Controls
          Positioned(
            bottom: 30,
            left: 16,
            right: 16,
            child: Row(
              children: [
                // Response Input
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: TextField(
                      controller: _responseController,
                      style: GoogleFonts.poppins(color: Colors.white),
                      decoration: InputDecoration(
                        hintText: 'Respond to viewers...',
                        hintStyle: GoogleFonts.poppins(color: Colors.white70),
                        border: InputBorder.none,
                      ),
                      onSubmitted: (text) {
                        if (text.isNotEmpty) {
                          setState(() {
                            _comments.add('Host: $text');
                          });
                          _responseController.clear();
                        }
                      },
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // Mute Button
                GestureDetector(
                  onTap: () => setState(() => _isMuted = !_isMuted),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _isMuted
                          ? Colors.red
                          : Colors.white.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _isMuted ? Icons.mic_off : Icons.mic,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Video Toggle
                GestureDetector(
                  onTap: () => setState(() => _isVideoOn = !_isVideoOn),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: !_isVideoOn
                          ? Colors.red
                          : Colors.white.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _isVideoOn ? Icons.videocam : Icons.videocam_off,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // End Live Button
                GestureDetector(
                  onTap: _endLiveStream,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.call_end,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _endLiveStream() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2A2A2A),
        title: Text(
          'End Live Stream',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Are you sure you want to end your live stream?',
              style: GoogleFonts.poppins(
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.3)),
              ),
              child: Column(
                children: [
                  Text(
                    'Session Summary',
                    style: GoogleFonts.poppins(
                      color: const Color(0xFF4CAF50),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Viewers:',
                        style: GoogleFonts.poppins(color: Colors.white70),
                      ),
                      Text(
                        '$_viewerCount',
                        style: GoogleFonts.poppins(color: Colors.white),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Earnings:',
                        style: GoogleFonts.poppins(color: Colors.white70),
                      ),
                      Text(
                        '₹${_earnings.toStringAsFixed(0)}',
                        style: GoogleFonts.poppins(color: Colors.white),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Continue',
              style: GoogleFonts.poppins(
                color: Colors.grey,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text(
              'End Stream',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
