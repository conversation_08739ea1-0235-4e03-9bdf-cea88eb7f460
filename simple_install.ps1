Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    FRIENDY APP - SIMPLE INSTALLER" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if APK exists
$apkPath = "build\app\outputs\flutter-apk\app-debug.apk"
if (Test-Path $apkPath) {
    Write-Host "✅ APK found: $apkPath" -ForegroundColor Green
    $apkSize = (Get-Item $apkPath).Length / 1MB
    Write-Host "📱 APK Size: $([math]::Round($apkSize, 1)) MB" -ForegroundColor Yellow
} else {
    Write-Host "❌ APK not found. Building now..." -ForegroundColor Red
    Write-Host "Building APK..." -ForegroundColor Yellow
    flutter build apk --debug
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Build failed!" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "🔍 Checking for connected devices..." -ForegroundColor Yellow

# Try to detect devices with timeout
$devices = flutter devices --machine 2>$null | ConvertFrom-Json -ErrorAction SilentlyContinue
$androidDevice = $devices | Where-Object { $_.platform -eq "android" } | Select-Object -First 1

if ($androidDevice) {
    Write-Host "✅ Android device found: $($androidDevice.name)" -ForegroundColor Green
    Write-Host "📱 Device ID: $($androidDevice.id)" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "🚀 Installing Friendy app..." -ForegroundColor Yellow
    
    # Method 1: Flutter install
    Write-Host "Method 1: Flutter install..." -ForegroundColor Cyan
    flutter install -d $androidDevice.id
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "🎉 SUCCESS! Friendy app installed!" -ForegroundColor Green
        Write-Host "📱 Check your device for the Friendy app icon" -ForegroundColor Yellow
        exit 0
    }
    
    # Method 2: Direct ADB
    Write-Host "Method 2: Direct ADB install..." -ForegroundColor Cyan
    adb -s $androidDevice.id install -r $apkPath
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "🎉 SUCCESS! Friendy app installed!" -ForegroundColor Green
        Write-Host "📱 Check your device for the Friendy app icon" -ForegroundColor Yellow
        exit 0
    }
    
    Write-Host "❌ Automatic installation failed" -ForegroundColor Red
} else {
    Write-Host "❌ No Android device detected" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "    MANUAL INSTALLATION REQUIRED" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
Write-Host ""
Write-Host "📁 APK Location: $((Get-Item $apkPath).FullName)" -ForegroundColor Cyan
Write-Host ""
Write-Host "📱 Manual Installation Steps:" -ForegroundColor White
Write-Host "1. Copy the APK file to your phone" -ForegroundColor Gray
Write-Host "2. Enable 'Install from Unknown Sources' in Settings" -ForegroundColor Gray
Write-Host "3. Tap the APK file to install" -ForegroundColor Gray
Write-Host "4. Look for 'Friendy' app icon on your device" -ForegroundColor Gray
Write-Host ""
Write-Host "✨ Your Friendy dating app is ready!" -ForegroundColor Green
