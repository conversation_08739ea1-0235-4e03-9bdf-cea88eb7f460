import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:friendy/main.dart';

void main() {
  group('Friendy App Tests', () {
    testWidgets('App should start without crashing',
        (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const FriendyApp());

      // Verify that the app starts without crashing
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('App should show loading or onboarding',
        (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const FriendyApp());

      // Wait for the app to settle
      await tester.pumpAndSettle();

      // Verify that either loading screen or onboarding is shown
      expect(
        find.byType(MaterialApp).or(find.byType(CircularProgressIndicator)),
        findsOneWidget,
      );
    });
  });

  group('Basic Widget Tests', () {
    testWidgets('MaterialApp should be created', (WidgetTester tester) async {
      await tester.pumpWidget(const FriendyApp());
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('App should have correct title', (WidgetTester tester) async {
      await tester.pumpWidget(const FriendyApp());

      final MaterialApp app = tester.widget(find.byType(MaterialApp));
      expect(app.title, equals('Friendy - Dating App'));
    });
  });
}

extension on Finder {
  or(Finder byType) {}
}
