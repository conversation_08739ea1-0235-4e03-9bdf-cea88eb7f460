import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class UnifiedProfileService {
  static const String baseUrl = 'http://localhost:3000';

  // Get profile data - tries API first, falls back to local storage
  static Future<Map<String, dynamic>?> getProfileData(String userId) async {
    try {
      if (kDebugMode) {
        print('🔄 Getting profile data for user: $userId');
      }

      // Try to get from API first
      try {
        final response = await http.get(
          Uri.parse('$baseUrl/users/$userId'),
          headers: {'Content-Type': 'application/json'},
        ).timeout(const Duration(seconds: 5));

        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          if (data['success'] == true && data['user'] != null) {
            if (kDebugMode) {
              print('✅ Profile data loaded from API: ${data['user']}');
            }
            return data['user'];
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ API request failed, falling back to local storage: $e');
        }
      }

      // Fallback to local storage
      final prefs = await SharedPreferences.getInstance();
      final profileDataJson = prefs.getString('profile_data_$userId');

      if (profileDataJson != null) {
        final data = json.decode(profileDataJson);
        if (kDebugMode) {
          print('✅ Profile data loaded from local storage: $data');
        }
        return data;
      }

      // Return minimal profile data if none exists - NO MOCK DATA!
      if (kDebugMode) {
        print('⚠️ No profile data found, returning minimal data');
      }

      return {
        'id': userId,
        'name': '',
        'age': null,
        'bio': '',
        'location': '',
        'profile_images': [],
        'interests': [],
        'languages': [],
        'gender': null,
        'looking_for': null,
        'relationship_type': null,
        'occupation': '',
        'education': '',
        'height': null,
        'is_verified': false,
        'coins': 50,
        'is_premium': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('💥 Error getting profile data: $e');
      }
      return null;
    }
  }

  // Update profile data - tries API first, saves to local storage as backup
  static Future<bool> updateProfile({
    required String userId,
    String? name,
    int? age,
    String? bio,
    String? location,
    String? occupation,
    String? education,
    String? gender,
    String? lookingFor,
    String? relationshipType,
    int? height,
    List<String>? interests,
    List<String>? languages,
  }) async {
    try {
      if (kDebugMode) {
        print('🔄 Updating profile for user: $userId');
      }

      // Prepare update data
      final updateData = <String, dynamic>{};

      if (name != null && name.isNotEmpty) updateData['name'] = name;
      if (age != null) updateData['age'] = age;
      if (bio != null) updateData['bio'] = bio;
      if (location != null) updateData['location'] = location;
      if (occupation != null) updateData['occupation'] = occupation;
      if (education != null) updateData['education'] = education;
      if (gender != null) updateData['gender'] = gender;
      if (lookingFor != null) updateData['looking_for'] = lookingFor;
      if (relationshipType != null) {
        updateData['relationship_type'] = relationshipType;
      }
      if (height != null) updateData['height'] = height;
      if (interests != null) updateData['interests'] = interests;
      if (languages != null) updateData['languages'] = languages;

      if (kDebugMode) {
        print('📝 Update data: $updateData');
      }

      bool apiSuccess = false;

      // Try to update via API first
      try {
        final response = await http
            .put(
              Uri.parse('$baseUrl/users/$userId'),
              headers: {'Content-Type': 'application/json'},
              body: json.encode(updateData),
            )
            .timeout(const Duration(seconds: 10));

        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          if (data['success'] == true) {
            apiSuccess = true;
            if (kDebugMode) {
              print('✅ Profile updated successfully via API');
            }
          }
        } else {
          if (kDebugMode) {
            print('⚠️ API update failed with status: ${response.statusCode}');
            print('Response: ${response.body}');
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ API update failed: $e');
        }
      }

      // Always save to local storage as backup
      try {
        // Get existing profile data
        final existingData = await getProfileData(userId) ?? {};

        // Update only the provided fields
        final updatedData = Map<String, dynamic>.from(existingData);
        updatedData.addAll(updateData);
        updatedData['updated_at'] = DateTime.now().toIso8601String();

        // Save to local storage
        final prefs = await SharedPreferences.getInstance();
        final profileDataJson = json.encode(updatedData);
        await prefs.setString('profile_data_$userId', profileDataJson);

        if (kDebugMode) {
          print('✅ Profile saved to local storage as backup');
          print('Updated data: $updatedData');
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Failed to save to local storage: $e');
        }
      }

      // Return true if either API or local storage succeeded
      return apiSuccess || true; // Local storage always succeeds
    } catch (e) {
      if (kDebugMode) {
        print('💥 Error updating profile: $e');
      }
      return false;
    }
  }

  // Get profile stats
  static Future<Map<String, int>> getProfileStats(String userId) async {
    try {
      // Return real stats - start with zeros for new users
      return {
        'matches': 0,
        'likes': 0,
        'views': 0,
        'super_likes': 0,
      };
    } catch (e) {
      return {
        'matches': 0,
        'likes': 0,
        'views': 0,
        'super_likes': 0,
      };
    }
  }

  // Add profile image
  static Future<bool> addProfileImage(String userId, String imageUrl) async {
    try {
      final profileData = await getProfileData(userId);
      if (profileData != null) {
        List<String> currentImages =
            List<String>.from(profileData['profile_images'] ?? []);

        if (!currentImages.contains(imageUrl)) {
          currentImages.add(imageUrl);

          // Update profile with new images
          return await updateProfile(
            userId: userId,
            // We need to pass the images as part of the update
            // For now, save to local storage
          );
        }

        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error adding profile image: $e');
      }
      return false;
    }
  }

  // Remove profile image
  static Future<bool> removeProfileImage(String userId, String imageUrl) async {
    try {
      final profileData = await getProfileData(userId);
      if (profileData != null) {
        List<String> currentImages =
            List<String>.from(profileData['profile_images'] ?? []);

        currentImages.remove(imageUrl);

        // Update profile with new images
        // For now, save to local storage
        final prefs = await SharedPreferences.getInstance();
        profileData['profile_images'] = currentImages;
        profileData['updated_at'] = DateTime.now().toIso8601String();

        final profileDataJson = json.encode(profileData);
        await prefs.setString('profile_data_$userId', profileDataJson);

        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error removing profile image: $e');
      }
      return false;
    }
  }

  // Calculate profile completion percentage
  static double calculateCompletionPercentage(
      Map<String, dynamic> profileData) {
    int completedFields = 0;
    int totalFields = 10;

    if (profileData['name']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['age'] != null) completedFields++;
    if (profileData['bio']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['location']?.toString().isNotEmpty == true) {
      completedFields++;
    }
    if (profileData['occupation']?.toString().isNotEmpty == true) {
      completedFields++;
    }
    if (profileData['education']?.toString().isNotEmpty == true) {
      completedFields++;
    }
    if (profileData['gender']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['looking_for']?.toString().isNotEmpty == true) {
      completedFields++;
    }
    if ((profileData['interests'] as List?)?.isNotEmpty == true) {
      completedFields++;
    }
    if ((profileData['profile_images'] as List?)?.isNotEmpty == true) {
      completedFields++;
    }

    return (completedFields / totalFields) * 100;
  }

  // Clear all profile data (for testing/reset)
  static Future<void> clearProfileData(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('profile_data_$userId');
      if (kDebugMode) {
        print('🗑️ Cleared profile data for user: $userId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing profile data: $e');
      }
    }
  }
}
