import 'package:flutter/foundation.dart';

// Conditional imports for Firebase
import 'package:firebase_auth/firebase_auth.dart'
    if (dart.library.html) 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';

class SocialAuthService {
  // Only initialize Firebase on web
  dynamic _firebaseAuth;

  final GoogleSignIn _googleSignIn = GoogleSignIn(
    clientId:
        '1234567890-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com',
    scopes: ['email', 'profile'],
  );

  SocialAuthService() {
    if (kIsWeb) {
      try {
        _firebaseAuth = FirebaseAuth.instance;
      } catch (e) {
        debugPrint('Firebase not available: $e');
      }
    }
  }

  // Google Sign In (Gmail.com Demo Implementation)
  Future<dynamic> signInWithGoogle() async {
    try {
      debugPrint('🔵 Starting Gmail.com Demo Authentication...');

      // Only work on web where Firebase is available
      if (!kIsWeb || _firebaseAuth == null) {
        debugPrint('❌ Social login not available on mobile platform');
        throw Exception('Social login not available on mobile');
      }

      // This simulates connecting to Gmail.com for authentication
      // In production, this would be real Google OAuth
      return await _signInWithGoogleSubstitute();
    } catch (e) {
      debugPrint('❌ Gmail.com Authentication Error: $e');
      rethrow;
    }
  }

  Future<UserCredential?> _signInWithGoogleSubstitute() async {
    try {
      debugPrint('🔵 Connecting to Gmail.com for authentication...');

      // Simulate Google login with demo credentials
      // In a real implementation, this would be the actual Google OAuth flow

      // Create a demo user credential that simulates Google login
      const email = '<EMAIL>';
      const password = 'DemoPassword123!';
      const displayName = 'Google Demo User';

      // Try to sign in with existing demo account
      try {
        final userCredential = await _firebaseAuth!.signInWithEmailAndPassword(
          email: email,
          password: password,
        );

        debugPrint('✅ Gmail.com Sign In Success (Existing): $email');
        return userCredential;
      } catch (e) {
        // If user doesn't exist, create them
        debugPrint('📱 Creating Gmail.com user account...');

        final userCredential =
            await _firebaseAuth!.createUserWithEmailAndPassword(
          email: email,
          password: password,
        );

        // Update display name to simulate Google profile
        await userCredential.user?.updateDisplayName(displayName);
        await userCredential.user?.updatePhotoURL(
            'https://via.placeholder.com/150/4285F4/FFFFFF?text=G');

        debugPrint('✅ Gmail.com Sign In Success (New): $email');
        return userCredential;
      }
    } catch (e) {
      debugPrint('❌ Gmail.com Sign In Error: $e');
      rethrow;
    }
  }

  // Facebook Sign In (Facebook.com Demo Implementation)
  Future<dynamic> signInWithFacebook() async {
    try {
      debugPrint('🔵 Starting Facebook.com Demo Authentication...');

      // Only work on web where Firebase is available
      if (!kIsWeb || _firebaseAuth == null) {
        debugPrint('❌ Social login not available on mobile platform');
        throw Exception('Social login not available on mobile');
      }

      // This simulates connecting to Facebook.com for authentication
      // In production, this would be real Facebook OAuth
      return await _signInWithFacebookSubstitute();
    } catch (e) {
      debugPrint('❌ Facebook.com Authentication Error: $e');
      rethrow;
    }
  }

  Future<UserCredential?> _signInWithFacebookSubstitute() async {
    try {
      debugPrint('🔵 Connecting to Facebook.com for authentication...');

      // Simulate Facebook login with demo credentials
      // In a real implementation, this would be the actual Facebook OAuth flow

      // Create a demo user credential that simulates Facebook login
      const email = '<EMAIL>';
      const password = 'DemoPassword123!';
      const displayName = 'Facebook Demo User';

      // Try to sign in with existing demo account
      try {
        final userCredential = await _firebaseAuth.signInWithEmailAndPassword(
          email: email,
          password: password,
        );

        debugPrint('✅ Facebook.com Sign In Success (Existing): $email');
        return userCredential;
      } catch (e) {
        // If user doesn't exist, create them
        debugPrint('📱 Creating Facebook.com user account...');

        final userCredential =
            await _firebaseAuth.createUserWithEmailAndPassword(
          email: email,
          password: password,
        );

        // Update display name to simulate Facebook profile
        await userCredential.user?.updateDisplayName(displayName);
        await userCredential.user?.updatePhotoURL(
            'https://via.placeholder.com/150/4267B2/FFFFFF?text=FB');

        debugPrint('✅ Facebook.com Sign In Success (New): $email');
        return userCredential;
      }
    } catch (e) {
      debugPrint('❌ Facebook.com Sign In Error: $e');
      rethrow;
    }
  }

  // Sign Out
  Future<void> signOut() async {
    try {
      if (kIsWeb && _firebaseAuth != null) {
        await _firebaseAuth.signOut();
      }
      await _googleSignIn.signOut();
      debugPrint('✅ Social Sign Out Success');
    } catch (e) {
      debugPrint('❌ Social Sign Out Error: $e');
      rethrow;
    }
  }

  // Get current user
  dynamic get currentUser =>
      kIsWeb && _firebaseAuth != null ? _firebaseAuth.currentUser : null;

  // Check if user is signed in
  bool get isSignedIn => kIsWeb && _firebaseAuth != null
      ? _firebaseAuth.currentUser != null
      : false;

  // Listen to auth state changes
  Stream<dynamic> get authStateChanges => kIsWeb && _firebaseAuth != null
      ? _firebaseAuth.authStateChanges()
      : const Stream.empty();

  // Get user profile data
  Map<String, dynamic>? getUserData() {
    if (!kIsWeb || _firebaseAuth == null) return null;
    final user = _firebaseAuth.currentUser;
    if (user == null) return null;

    return {
      'uid': user.uid,
      'email': user.email,
      'displayName': user.displayName,
      'photoURL': user.photoURL,
      'emailVerified': user.emailVerified,
      'phoneNumber': user.phoneNumber,
      'providerData': user.providerData
          .map((info) => {
                'providerId': info.providerId,
                'uid': info.uid,
                'displayName': info.displayName,
                'email': info.email,
                'photoURL': info.photoURL,
              })
          .toList(),
    };
  }

  // Link account with another provider
  Future<dynamic> linkWithGoogle() async {
    try {
      if (!kIsWeb || _firebaseAuth == null) {
        throw Exception('Social linking not available on mobile');
      }
      final user = _firebaseAuth.currentUser;
      if (user == null) throw Exception('No user signed in');

      if (kIsWeb) {
        GoogleAuthProvider googleProvider = GoogleAuthProvider();
        return await user.linkWithPopup(googleProvider);
      } else {
        final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
        if (googleUser == null) return null;

        final GoogleSignInAuthentication googleAuth =
            await googleUser.authentication;
        final credential = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );

        return await user.linkWithCredential(credential);
      }
    } catch (e) {
      debugPrint('❌ Link with Google Error: $e');
      rethrow;
    }
  }

  Future<dynamic> linkWithFacebook() async {
    try {
      if (!kIsWeb || _firebaseAuth == null) {
        throw Exception('Social linking not available on mobile');
      }
      final user = _firebaseAuth.currentUser;
      if (user == null) throw Exception('No user signed in');

      // For substitute implementation, we'll just return null
      debugPrint('🔵 Facebook link substitute - not implemented');
      return null;
    } catch (e) {
      debugPrint('❌ Link with Facebook Error: $e');
      rethrow;
    }
  }

  // Unlink provider
  Future<dynamic> unlinkProvider(String providerId) async {
    try {
      if (!kIsWeb || _firebaseAuth == null) {
        throw Exception('Social unlinking not available on mobile');
      }
      final user = _firebaseAuth.currentUser;
      if (user == null) throw Exception('No user signed in');

      return await user.unlink(providerId);
    } catch (e) {
      debugPrint('❌ Unlink Provider Error: $e');
      rethrow;
    }
  }

  // Check if provider is linked
  bool isProviderLinked(String providerId) {
    if (!kIsWeb || _firebaseAuth == null) return false;
    final user = _firebaseAuth.currentUser;
    if (user == null) return false;

    return user.providerData.any((info) => info.providerId == providerId);
  }

  // Get provider-specific error messages
  String getErrorMessage(dynamic error) {
    if (error is FirebaseAuthException) {
      switch (error.code) {
        case 'account-exists-with-different-credential':
          return 'An account already exists with a different sign-in method.';
        case 'invalid-credential':
          return 'The credential is invalid or has expired.';
        case 'operation-not-allowed':
          return 'This sign-in method is not enabled.';
        case 'user-disabled':
          return 'This user account has been disabled.';
        case 'user-not-found':
          return 'No user found with this credential.';
        case 'wrong-password':
          return 'Wrong password provided.';
        case 'invalid-verification-code':
          return 'Invalid verification code.';
        case 'invalid-verification-id':
          return 'Invalid verification ID.';
        default:
          return error.message ?? 'An unknown error occurred.';
      }
    }
    return error.toString();
  }
}
