import 'package:flutter/material.dart';
import 'dart:math' as math;

class CircularMatchPage extends StatefulWidget {
  const CircularMatchPage({super.key});

  @override
  State<CircularMatchPage> createState() => _CircularMatchPageState();
}

class _CircularMatchPageState extends State<CircularMatchPage>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _particleController;
  late AnimationController _glowController;
  late AnimationController _waveController;

  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _particleAnimation;
  late Animation<double> _glowAnimation;
  late Animation<double> _waveAnimation;

  final int _matchCount = 9;
  final int _selectedProfileIndex = -1;

  // Beautiful host profiles with realistic data
  final List<Map<String, dynamic>> _profiles = [
    {
      'name': '<PERSON><PERSON> <PERSON>',
      'age': 24,
      'image':
          'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face',
      'isOnline': true,
      'distance': '2.1 km',
      'interests': ['Photography', 'Travel', 'Fashion'],
      'bio': 'Love capturing beautiful moments ✨',
      'rating': 4.8,
      'hostType': 'Premium',
      'price': '₹2,500/hour',
      'verified': true,
    },
    {
      'name': 'Ananya Singh',
      'age': 26,
      'image':
          'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
      'isOnline': true,
      'distance': '1.5 km',
      'interests': ['Music', 'Art', 'Dancing'],
      'bio': 'Artist by day, dancer by night 💃',
      'rating': 4.9,
      'hostType': 'VIP',
      'price': '₹3,500/hour',
      'verified': true,
    },
    {
      'name': 'Kavya Patel',
      'age': 23,
      'image':
          'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop&crop=face',
      'isOnline': false,
      'distance': '3.2 km',
      'interests': ['Fitness', 'Cooking', 'Yoga'],
      'bio': 'Fitness enthusiast & chef 🍳',
      'rating': 4.7,
      'hostType': 'Premium',
      'price': '₹2,000/hour',
      'verified': true,
    },
    {
      'name': 'Riya Gupta',
      'age': 25,
      'image':
          'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&h=400&fit=crop&crop=face',
      'isOnline': true,
      'distance': '0.8 km',
      'interests': ['Reading', 'Movies', 'Coffee'],
      'bio': 'Bookworm & coffee lover ☕',
      'rating': 4.6,
      'hostType': 'Standard',
      'price': '₹1,800/hour',
      'verified': false,
    },
    {
      'name': 'Meera Joshi',
      'age': 27,
      'image':
          'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop&crop=face',
      'isOnline': true,
      'distance': '2.2 km',
      'interests': ['Nature', 'Meditation', 'Books'],
      'bio': 'Finding peace in nature 🌿',
      'rating': 4.8,
      'hostType': 'VIP',
      'price': '₹3,000/hour',
      'verified': true,
    },
    {
      'name': 'Isha Reddy',
      'age': 22,
      'image':
          'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=400&h=400&fit=crop&crop=face',
      'isOnline': false,
      'distance': '4.1 km',
      'interests': ['Gaming', 'Tech', 'Anime'],
      'bio': 'Gamer girl & tech geek 🎮',
      'rating': 4.5,
      'hostType': 'Premium',
      'price': '₹2,200/hour',
      'verified': true,
    },
  ];

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _rotationController = AnimationController(
      duration: const Duration(seconds: 25),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _particleController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );
    _waveController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    // Initialize animations
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.95,
      end: 1.15,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.elasticInOut,
    ));

    _particleAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _particleController,
      curve: Curves.linear,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    _waveAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _waveController,
      curve: Curves.linear,
    ));

    // Start animations
    _rotationController.repeat();
    _pulseController.repeat(reverse: true);
    _particleController.repeat();
    _glowController.repeat(reverse: true);
    _waveController.repeat();
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    _particleController.dispose();
    _glowController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A), // Dark background #1a1a1a
      body: Stack(
        children: [
          // Animated background particles
          _buildAnimatedBackground(),
          // Main content
          SafeArea(
            child: Column(
              children: [
                _buildEnhancedHeader(),
                Expanded(
                  child: _buildMagicalCircularInterface(),
                ),
                _buildEnhancedBottomSection(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Hot badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFFFF6B35), Color(0xFFFF8E53)],
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.local_fire_department,
                    color: Colors.white, size: 16),
                SizedBox(width: 4),
                Text(
                  'HOT!',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          // Coin counter
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.monetization_on, color: Colors.yellow, size: 16),
                SizedBox(width: 4),
                Text(
                  '0',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCircularMatchInterface() {
    return Center(
      child: SizedBox(
        width: 300,
        height: 300,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Rotating profiles around the circle
            AnimatedBuilder(
              animation: _rotationAnimation,
              builder: (context, child) {
                return Stack(
                  alignment: Alignment.center,
                  children: _profiles.asMap().entries.map((entry) {
                    final index = entry.key;
                    final profile = entry.value;
                    final angle = (2 * math.pi / _profiles.length) * index +
                        _rotationAnimation.value;
                    const radius = 120.0;

                    final x = radius * math.cos(angle);
                    final y = radius * math.sin(angle);

                    return Transform.translate(
                      offset: Offset(x, y),
                      child: _buildProfileCircle(profile),
                    );
                  }).toList(),
                );
              },
            ),
            // Center user avatar
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 3),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.white.withValues(alpha: 0.3),
                          blurRadius: 20,
                          spreadRadius: 5,
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        'assets/images/favicon.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            decoration: const BoxDecoration(
                              gradient: LinearGradient(
                                colors: [Color(0xFFFF6B9D), Color(0xFF9B59B6)],
                              ),
                            ),
                            child: const Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 40,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
            ),
            // Connecting lines/dots
            AnimatedBuilder(
              animation: _rotationAnimation,
              builder: (context, child) {
                return CustomPaint(
                  size: const Size(300, 300),
                  painter: ConnectionPainter(_rotationAnimation.value),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileCircle(Map<String, dynamic> profile) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: profile['isOnline'] ? Colors.green : Colors.grey,
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: (profile['isOnline'] ? Colors.green : Colors.grey)
                .withValues(alpha: 0.5),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: ClipOval(
        child: Image.asset(
          profile['image'],
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.primaries[
                        profile['name'].hashCode % Colors.primaries.length],
                    Colors.primaries[(profile['name'].hashCode + 1) %
                        Colors.primaries.length],
                  ],
                ),
              ),
              child: Center(
                child: Text(
                  profile['name'][0].toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBottomSection() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          // Random Match Button
          Container(
            width: double.infinity,
            height: 56,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFFFF6B35), Color(0xFFFF8E53)],
              ),
              borderRadius: BorderRadius.circular(28),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFFF6B35).withValues(alpha: 0.4),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: _startRandomMatch,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                ),
              ),
              child: const Text(
                'Random Match',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Match counter
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.monetization_on, color: Colors.yellow, size: 20),
              const SizedBox(width: 8),
              Text(
                '$_matchCount/match',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _startRandomMatch() {
    // Implement random matching logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('🎯 Searching for your perfect match...'),
        backgroundColor: const Color(0xFFFF6B9D),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        margin: const EdgeInsets.all(20),
      ),
    );
  }

  // Enhanced animated background with floating particles
  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _particleAnimation,
      builder: (context, child) {
        return CustomPaint(
          painter: ParticleBackgroundPainter(_particleAnimation.value),
          size: Size.infinite,
        );
      },
    );
  }

  // Enhanced header with glassmorphism effect
  Widget _buildEnhancedHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.1),
            Colors.white.withValues(alpha: 0.05),
          ],
        ),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Animated HOT badge
          AnimatedBuilder(
            animation: _glowAnimation,
            builder: (context, child) {
              return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Color.lerp(const Color(0xFFFF6B35),
                          const Color(0xFFFF8E53), _glowAnimation.value)!,
                      const Color(0xFFFF8E53),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFFFF6B35)
                          .withValues(alpha: _glowAnimation.value * 0.6),
                      blurRadius: 15,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.local_fire_department,
                      color: Colors.white,
                      size: 18,
                      shadows: [
                        Shadow(
                          color: Colors.orange.withValues(alpha: 0.8),
                          blurRadius: 10,
                        ),
                      ],
                    ),
                    const SizedBox(width: 6),
                    const Text(
                      'HOT!',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        shadows: [
                          Shadow(
                            color: Colors.black26,
                            blurRadius: 2,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          // Enhanced coin counter
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFFFD700).withValues(alpha: 0.4),
                  blurRadius: 15,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.monetization_on,
                  color: Colors.white,
                  size: 18,
                  shadows: [
                    Shadow(
                      color: Colors.orange.withValues(alpha: 0.8),
                      blurRadius: 5,
                    ),
                  ],
                ),
                const SizedBox(width: 6),
                const Text(
                  '1,250',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    shadows: [
                      Shadow(
                        color: Colors.black26,
                        blurRadius: 2,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Magical circular interface with enhanced visuals
  Widget _buildMagicalCircularInterface() {
    return Center(
      child: SizedBox(
        width: 400,
        height: 400,
        child: AnimatedBuilder(
          animation: Listenable.merge([
            _rotationAnimation,
            _pulseAnimation,
            _waveAnimation,
          ]),
          builder: (context, child) {
            return Stack(
              alignment: Alignment.center,
              children: [
                // Outer glow rings
                _buildGlowRings(),
                // Connecting energy lines
                CustomPaint(
                  painter: EnergyLinesPainter(
                    _rotationAnimation.value,
                    _waveAnimation.value,
                  ),
                  size: const Size(400, 400),
                ),
                // Profile circles
                ..._profiles.asMap().entries.map((entry) {
                  final index = entry.key;
                  final profile = entry.value;
                  final angle = (2 * math.pi / _profiles.length) * index +
                      _rotationAnimation.value;
                  const radius = 140.0;

                  final x = radius * math.cos(angle);
                  final y = radius * math.sin(angle);

                  return Transform.translate(
                    offset: Offset(x, y),
                    child: _buildEnhancedProfileCircle(profile, index),
                  );
                }),
                // Center avatar with magical effects
                _buildMagicalCenterAvatar(),
              ],
            );
          },
        ),
      ),
    );
  }

  // Enhanced bottom section with modern design
  Widget _buildEnhancedBottomSection() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Match counter with glow effect
          AnimatedBuilder(
            animation: _glowAnimation,
            builder: (context, child) {
              return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFFFF6B9D).withValues(alpha: 0.8),
                      const Color(0xFF9B59B6).withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFFFF6B9D)
                          .withValues(alpha: _glowAnimation.value * 0.6),
                      blurRadius: 20,
                      spreadRadius: 2,
                    ),
                  ],
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  '$_matchCount/match',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        color: Colors.black26,
                        blurRadius: 4,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 20),
          // Enhanced Random Match button
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value * 0.95 + 0.05,
                child: Container(
                  width: 200,
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [
                        Color(0xFFFF6B9D),
                        Color(0xFFFF8E53),
                        Color(0xFFFF6B9D),
                      ],
                      stops: [0.0, 0.5, 1.0],
                    ),
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFFFF6B9D).withValues(alpha: 0.6),
                        blurRadius: 25,
                        spreadRadius: 3,
                        offset: const Offset(0, 8),
                      ),
                      BoxShadow(
                        color: Colors.white.withValues(alpha: 0.1),
                        blurRadius: 10,
                        spreadRadius: -2,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(30),
                      onTap: _startRandomMatch,
                      child: const Center(
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.shuffle,
                              color: Colors.white,
                              size: 24,
                              shadows: [
                                Shadow(
                                  color: Colors.black26,
                                  blurRadius: 4,
                                ),
                              ],
                            ),
                            SizedBox(width: 12),
                            Text(
                              'Random Match',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(
                                    color: Colors.black26,
                                    blurRadius: 4,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // Glow rings around the interface
  Widget _buildGlowRings() {
    return AnimatedBuilder(
      animation: _waveAnimation,
      builder: (context, child) {
        return Stack(
          alignment: Alignment.center,
          children: List.generate(3, (index) {
            final scale = 1.0 +
                (index * 0.3) +
                (math.sin(_waveAnimation.value + index) * 0.1);
            final opacity = (1.0 - (index * 0.3)) * 0.3;

            return Transform.scale(
              scale: scale,
              child: Container(
                width: 300,
                height: 300,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: const Color(0xFFFF6B9D).withValues(alpha: opacity),
                    width: 2,
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }

  // Professional host profile circle with attractive design
  Widget _buildEnhancedProfileCircle(Map<String, dynamic> profile, int index) {
    return AnimatedBuilder(
      animation: _glowAnimation,
      builder: (context, child) {
        final isOnline = profile['isOnline'] as bool;
        final profileColor = profile['color'] as Color;
        final hostType = profile['hostType'] as String;
        final rating = profile['rating'] as double;

        return GestureDetector(
          onTap: () => _showProfileDetails(profile),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Main profile container
              Container(
                width: 90,
                height: 90,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      profileColor.withValues(alpha: 0.9),
                      profileColor.withValues(alpha: 0.6),
                      profileColor.withValues(alpha: 0.3),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: profileColor.withValues(
                          alpha: _glowAnimation.value * 0.8),
                      blurRadius: 25,
                      spreadRadius: 4,
                    ),
                    if (isOnline)
                      BoxShadow(
                        color: const Color(0xFF00FF88).withValues(alpha: 0.7),
                        blurRadius: 20,
                        spreadRadius: 2,
                      ),
                  ],
                  border: Border.all(
                    color: isOnline
                        ? const Color(0xFF00FF88)
                        : Colors.white.withValues(alpha: 0.4),
                    width: 3,
                  ),
                ),
                child: ClipOval(
                  child: Stack(
                    children: [
                      // Professional avatar with initials and design
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              profileColor,
                              profileColor.withValues(alpha: 0.8),
                              profileColor.withValues(alpha: 0.6),
                            ],
                          ),
                        ),
                        child: Stack(
                          children: [
                            // Background pattern
                            Positioned.fill(
                              child: CustomPaint(
                                painter: ProfilePatternPainter(profileColor),
                              ),
                            ),
                            // Profile initial
                            Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    profile['name'][0].toUpperCase(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 28,
                                      fontWeight: FontWeight.bold,
                                      shadows: [
                                        Shadow(
                                          color: Colors.black26,
                                          blurRadius: 6,
                                          offset: Offset(2, 2),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  // Rating stars
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: List.generate(5, (starIndex) {
                                      return Icon(
                                        starIndex < rating.floor()
                                            ? Icons.star
                                            : Icons.star_border,
                                        color: Colors.amber,
                                        size: 8,
                                        shadows: const [
                                          Shadow(
                                            color: Colors.black26,
                                            blurRadius: 2,
                                          ),
                                        ],
                                      );
                                    }),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Online indicator
                      if (isOnline)
                        Positioned(
                          bottom: 3,
                          right: 3,
                          child: Container(
                            width: 18,
                            height: 18,
                            decoration: BoxDecoration(
                              color: const Color(0xFF00FF88),
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 2),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(0xFF00FF88)
                                      .withValues(alpha: 0.8),
                                  blurRadius: 10,
                                  spreadRadius: 2,
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.circle,
                              color: Color(0xFF00FF88),
                              size: 8,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              // Host type badge
              Positioned(
                top: -5,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: hostType == 'VIP'
                          ? [const Color(0xFFFFD700), const Color(0xFFFFA500)]
                          : hostType == 'Premium'
                              ? [
                                  const Color(0xFF9B59B6),
                                  const Color(0xFF8E44AD)
                                ]
                              : [
                                  const Color(0xFF95A5A6),
                                  const Color(0xFF7F8C8D)
                                ],
                    ),
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    hostType.toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          color: Colors.black26,
                          blurRadius: 2,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // Name label
              Positioned(
                bottom: -20,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.black.withValues(alpha: 0.7),
                        Colors.black.withValues(alpha: 0.5),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    profile['name'],
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      shadows: [
                        Shadow(
                          color: Colors.black26,
                          blurRadius: 2,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Magical center avatar with enhanced effects
  Widget _buildMagicalCenterAvatar() {
    return AnimatedBuilder(
      animation: Listenable.merge([_pulseAnimation, _glowAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  const Color(0xFFFF6B9D).withValues(alpha: 0.9),
                  const Color(0xFF9B59B6).withValues(alpha: 0.7),
                  const Color(0xFF533483).withValues(alpha: 0.5),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFFF6B9D)
                      .withValues(alpha: _glowAnimation.value),
                  blurRadius: 30,
                  spreadRadius: 5,
                ),
                BoxShadow(
                  color: Colors.white.withValues(alpha: 0.3),
                  blurRadius: 15,
                  spreadRadius: -2,
                  offset: const Offset(0, -5),
                ),
              ],
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.4),
                width: 3,
              ),
            ),
            child: ClipOval(
              child: Stack(
                children: [
                  // Center image
                  Image.asset(
                    'assets/images/favicon.png',
                    fit: BoxFit.cover,
                    width: 100,
                    height: 100,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Color(0xFFFF6B9D),
                              Color(0xFF9B59B6),
                            ],
                          ),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.favorite,
                            color: Colors.white,
                            size: 40,
                            shadows: [
                              Shadow(
                                color: Colors.black26,
                                blurRadius: 8,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                  // Magical overlay effect
                  Container(
                    decoration: BoxDecoration(
                      gradient: RadialGradient(
                        colors: [
                          Colors.transparent,
                          const Color(0xFFFF6B9D).withValues(alpha: 0.1),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _showProfileDetails(Map<String, dynamic> profile) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        margin: const EdgeInsets.all(20),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF1A1A2E),
              const Color(0xFF16213E),
              (profile['color'] as Color).withValues(alpha: 0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: (profile['color'] as Color).withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Profile header
            Row(
              children: [
                // Profile avatar
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        profile['color'] as Color,
                        (profile['color'] as Color).withValues(alpha: 0.7),
                      ],
                    ),
                    border: Border.all(
                      color: profile['isOnline'] as bool
                          ? const Color(0xFF00FF88)
                          : Colors.white.withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      profile['name'][0].toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            '${profile['name']}, ${profile['age']}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: profile['hostType'] == 'VIP'
                                    ? [
                                        const Color(0xFFFFD700),
                                        const Color(0xFFFFA500)
                                      ]
                                    : profile['hostType'] == 'Premium'
                                        ? [
                                            const Color(0xFF9B59B6),
                                            const Color(0xFF8E44AD)
                                          ]
                                        : [
                                            const Color(0xFF95A5A6),
                                            const Color(0xFF7F8C8D)
                                          ],
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              profile['hostType'] as String,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            color: Colors.white.withValues(alpha: 0.7),
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${profile['distance']} away',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Row(
                            children: List.generate(5, (starIndex) {
                              return Icon(
                                starIndex <
                                        (profile['rating'] as double).floor()
                                    ? Icons.star
                                    : Icons.star_border,
                                color: Colors.amber,
                                size: 14,
                              );
                            }),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${profile['rating']}',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Online status
                if (profile['isOnline'] as bool)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: const Color(0xFF00FF88),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'ONLINE',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 20),
            // Interests
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Interests',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.9),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: (profile['interests'] as List<String>).map((interest) {
                return Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        (profile['color'] as Color).withValues(alpha: 0.6),
                        (profile['color'] as Color).withValues(alpha: 0.4),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    interest,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 24),
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF00FF88), Color(0xFF00CC6A)],
                      ),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF00FF88).withValues(alpha: 0.4),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(25),
                        onTap: () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                  '💬 Starting chat with ${profile['name']}...'),
                              backgroundColor: const Color(0xFF00FF88),
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                          );
                        },
                        child: const Center(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.chat, color: Colors.white, size: 20),
                              SizedBox(width: 8),
                              Text(
                                'Start Chat',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          (profile['color'] as Color),
                          (profile['color'] as Color).withValues(alpha: 0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: (profile['color'] as Color)
                              .withValues(alpha: 0.4),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(25),
                        onTap: () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('📞 Calling ${profile['name']}...'),
                              backgroundColor: profile['color'] as Color,
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                          );
                        },
                        child: const Center(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.videocam,
                                  color: Colors.white, size: 20),
                              SizedBox(width: 8),
                              Text(
                                'Video Call',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Particle background painter for magical effects
class ParticleBackgroundPainter extends CustomPainter {
  final double animation;

  ParticleBackgroundPainter(this.animation);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    // Draw floating particles
    for (int i = 0; i < 50; i++) {
      final x = (size.width * (i * 0.1 + animation * 0.5)) % size.width;
      final y = (size.height * (i * 0.07 + animation * 0.3)) % size.height;
      final radius = (math.sin(animation * 2 + i) * 2 + 3).abs();

      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Energy lines painter for connecting effects
class EnergyLinesPainter extends CustomPainter {
  final double rotation;
  final double wave;

  EnergyLinesPainter(this.rotation, this.wave);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFFF6B9D).withValues(alpha: 0.3)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    const radius = 140.0;

    // Draw energy lines between profiles
    for (int i = 0; i < 6; i++) {
      final angle1 = (2 * math.pi / 6) * i + rotation;
      final angle2 = (2 * math.pi / 6) * ((i + 1) % 6) + rotation;

      final point1 = Offset(
        center.dx + radius * math.cos(angle1),
        center.dy + radius * math.sin(angle1),
      );
      final point2 = Offset(
        center.dx + radius * math.cos(angle2),
        center.dy + radius * math.sin(angle2),
      );

      // Animated wave effect
      final waveOffset = math.sin(wave + i) * 10;
      final midPoint = Offset(
        (point1.dx + point2.dx) / 2 + waveOffset,
        (point1.dy + point2.dy) / 2 + waveOffset,
      );

      final path = Path()
        ..moveTo(point1.dx, point1.dy)
        ..quadraticBezierTo(midPoint.dx, midPoint.dy, point2.dx, point2.dy);

      canvas.drawPath(path, paint);
    }

    // Draw lines to center
    paint.color = const Color(0xFFFFD700).withValues(alpha: 0.4);
    for (int i = 0; i < 6; i++) {
      final angle = (2 * math.pi / 6) * i + rotation;
      final point = Offset(
        center.dx + radius * math.cos(angle),
        center.dy + radius * math.sin(angle),
      );

      canvas.drawLine(center, point, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class ConnectionPainter extends CustomPainter {
  final double rotation;

  ConnectionPainter(this.rotation);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.3)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    const radius = 120.0;

    // Draw connecting lines
    for (int i = 0; i < 4; i++) {
      final angle = (2 * math.pi / 4) * i + rotation;
      final x = radius * math.cos(angle);
      final y = radius * math.sin(angle);
      final profileCenter = Offset(center.dx + x, center.dy + y);

      canvas.drawLine(center, profileCenter, paint);

      // Draw small dots along the line
      final dotPaint = Paint()
        ..color = Colors.green
        ..style = PaintingStyle.fill;

      final midPoint = Offset(
        center.dx + x * 0.7,
        center.dy + y * 0.7,
      );
      canvas.drawCircle(midPoint, 3, dotPaint);
    }
  }

  @override
  bool shouldRepaint(ConnectionPainter oldDelegate) {
    return oldDelegate.rotation != rotation;
  }
}

// Profile pattern painter for attractive background designs
class ProfilePatternPainter extends CustomPainter {
  final Color color;

  ProfilePatternPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    final strokePaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.05)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Draw geometric pattern
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 3;

    // Draw concentric circles
    for (int i = 1; i <= 3; i++) {
      canvas.drawCircle(center, radius * i / 3, strokePaint);
    }

    // Draw radiating lines
    for (int i = 0; i < 8; i++) {
      final angle = (2 * math.pi / 8) * i;
      final startPoint = Offset(
        center.dx + (radius * 0.3) * math.cos(angle),
        center.dy + (radius * 0.3) * math.sin(angle),
      );
      final endPoint = Offset(
        center.dx + radius * math.cos(angle),
        center.dy + radius * math.sin(angle),
      );
      canvas.drawLine(startPoint, endPoint, strokePaint);
    }

    // Draw decorative dots
    for (int i = 0; i < 12; i++) {
      final angle = (2 * math.pi / 12) * i;
      final dotCenter = Offset(
        center.dx + (radius * 0.7) * math.cos(angle),
        center.dy + (radius * 0.7) * math.sin(angle),
      );
      canvas.drawCircle(dotCenter, 2, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
