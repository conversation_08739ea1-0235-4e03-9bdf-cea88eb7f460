import 'package:flutter/material.dart';
import 'dart:math';

class LiveVideoPage extends StatefulWidget {
  final Map<String, dynamic> host;

  const LiveVideoPage({super.key, required this.host});

  @override
  State<LiveVideoPage> createState() => _LiveVideoPageState();
}

class _LiveVideoPageState extends State<LiveVideoPage>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _heartController;
  final bool _isMuted = false;
  final bool _isVideoOn = true;
  int _viewerCount = 0;
  final List<String> _comments = [];
  final TextEditingController _commentController = TextEditingController();

  // Demo live video URLs
  final List<String> _demoVideos = [
    'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=400&h=600&fit=crop&crop=face',
    'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=600&fit=crop&crop=face',
    'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&h=600&fit=crop&crop=face',
  ];

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _heartController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _generateRandomViewerCount();
    _simulateComments();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _heartController.dispose();
    _commentController.dispose();
    super.dispose();
  }

  void _generateRandomViewerCount() {
    setState(() {
      _viewerCount = 150 + Random().nextInt(500);
    });

    // Update viewer count periodically
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _generateRandomViewerCount();
      }
    });
  }

  void _simulateComments() {
    final demoComments = [
      'Looking beautiful! 😍',
      'Hi from Mumbai!',
      'Love your style ❤️',
      'Can you say hi to me?',
      'You\'re amazing! 🔥',
      'Where are you from?',
      'Beautiful smile! 😊',
    ];

    Future.delayed(Duration(seconds: Random().nextInt(5) + 2), () {
      if (mounted && _comments.length < 20) {
        setState(() {
          _comments.add(demoComments[Random().nextInt(demoComments.length)]);
        });
        _simulateComments();
      }
    });
  }

  void _sendHeart() {
    _heartController.forward().then((_) {
      _heartController.reset();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Live Video Background
          Positioned.fill(
            child: Image.network(
              _demoVideos[0],
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[800],
                  child: const Center(
                    child: Icon(
                      Icons.videocam,
                      color: Colors.white,
                      size: 100,
                    ),
                  ),
                );
              },
            ),
          ),

          // Gradient Overlay
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.3),
                    Colors.black.withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
          ),

          // Top Header
          Positioned(
            top: 50,
            left: 16,
            right: 16,
            child: Row(
              children: [
                // Back Button
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                  ),
                ),

                // Host Info
                Expanded(
                  child: Row(
                    children: [
                      ClipOval(
                        child: Image.network(
                          widget.host['image'],
                          width: 40,
                          height: 40,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 40,
                              height: 40,
                              color: Colors.grey,
                              child:
                                  const Icon(Icons.person, color: Colors.white),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.host['name'],
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Row(
                              children: [
                                AnimatedBuilder(
                                  animation: _pulseController,
                                  builder: (context, child) {
                                    return Container(
                                      width: 8,
                                      height: 8,
                                      decoration: BoxDecoration(
                                        color: Colors.red.withValues(
                                          alpha: 0.5 +
                                              0.5 * _pulseController.value,
                                        ),
                                        shape: BoxShape.circle,
                                      ),
                                    );
                                  },
                                ),
                                const SizedBox(width: 6),
                                const Text(
                                  'LIVE',
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                const Icon(
                                  Icons.visibility,
                                  color: Colors.white70,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '$_viewerCount',
                                  style: const TextStyle(
                                    color: Colors.white70,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Follow Button
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFF6B9D),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Text(
                    'Follow',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Comments Section
          Positioned(
            bottom: 120,
            left: 16,
            right: 80,
            child: SizedBox(
              height: 200,
              child: ListView.builder(
                reverse: true,
                itemCount: _comments.length,
                itemBuilder: (context, index) {
                  final comment = _comments[_comments.length - 1 - index];
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      comment,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // Heart Animation
          Positioned(
            right: 20,
            bottom: 200,
            child: AnimatedBuilder(
              animation: _heartController,
              builder: (context, child) {
                return Transform.scale(
                  scale: 1.0 + _heartController.value * 0.5,
                  child: Opacity(
                    opacity: 1.0 - _heartController.value,
                    child: const Icon(
                      Icons.favorite,
                      color: Colors.red,
                      size: 40,
                    ),
                  ),
                );
              },
            ),
          ),

          // Bottom Controls
          Positioned(
            bottom: 30,
            left: 16,
            right: 16,
            child: Row(
              children: [
                // Comment Input
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: TextField(
                      controller: _commentController,
                      style: const TextStyle(color: Colors.white),
                      decoration: const InputDecoration(
                        hintText: 'Add a comment...',
                        hintStyle: TextStyle(color: Colors.white70),
                        border: InputBorder.none,
                      ),
                      onSubmitted: (text) {
                        if (text.isNotEmpty) {
                          setState(() {
                            _comments.add(text);
                          });
                          _commentController.clear();
                        }
                      },
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // Heart Button
                GestureDetector(
                  onTap: _sendHeart,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.favorite,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Share Button
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.share,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
