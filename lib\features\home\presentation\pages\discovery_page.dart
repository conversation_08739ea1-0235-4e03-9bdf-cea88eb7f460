import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/models/profile_model.dart';
import '../../data/services/discovery_service.dart';
import '../widgets/swipe_card.dart';
import '../widgets/action_buttons.dart';
import '../../../../core/providers/database_auth_provider.dart';

class DiscoveryPage extends StatefulWidget {
  const DiscoveryPage({super.key});

  @override
  State<DiscoveryPage> createState() => _DiscoveryPageState();
}

class _DiscoveryPageState extends State<DiscoveryPage>
    with TickerProviderStateMixin {
  final DiscoveryService _discoveryService = DiscoveryService();
  List<ProfileModel> _profiles = [];
  int _currentIndex = 0;
  bool _isLoading = true;

  late AnimationController _cardController;
  late AnimationController _buttonController;
  late Animation<double> _cardAnimation;
  late Animation<double> _buttonAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadProfiles();
  }

  void _setupAnimations() {
    _cardController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _cardAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cardController,
      curve: Curves.easeInOut,
    ));

    _buttonAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _buttonController,
      curve: Curves.elasticOut,
    ));
  }

  Future<void> _loadProfiles() async {
    try {
      setState(() => _isLoading = true);

      final authProvider =
          Provider.of<DatabaseAuthProvider>(context, listen: false);
      final currentUserId = authProvider.user?.id;

      if (currentUserId != null) {
        final profiles =
            await _discoveryService.getDiscoveryProfiles(currentUserId);
        setState(() {
          _profiles = profiles;
          _currentIndex = 0;
          _isLoading = false;
        });

        _cardController.forward();
        _buttonController.forward();
      }
    } catch (e) {
      setState(() => _isLoading = false);
      _showError('Failed to load profiles: ${e.toString()}');
    }
  }

  void _handleSwipe(SwipeDirection direction) async {
    if (_currentIndex >= _profiles.length) return;

    final currentProfile = _profiles[_currentIndex];
    final authProvider =
        Provider.of<DatabaseAuthProvider>(context, listen: false);
    final currentUserId = authProvider.user?.id;

    if (currentUserId == null) return;

    try {
      // Animate card out
      await _cardController.reverse();

      // Record the swipe action
      await _discoveryService.recordSwipe(
        currentUserId,
        currentProfile.id,
        direction == SwipeDirection.right,
      );

      // Check for match if it was a like
      if (direction == SwipeDirection.right) {
        final isMatch = await _discoveryService.checkForMatch(
          currentUserId,
          currentProfile.id,
        );

        if (isMatch) {
          _showMatchDialog(currentProfile);
        }
      }

      // Move to next profile
      setState(() {
        _currentIndex++;
      });

      // Animate new card in
      if (_currentIndex < _profiles.length) {
        _cardController.forward();
      } else {
        _loadMoreProfiles();
      }
    } catch (e) {
      _showError('Failed to process swipe: ${e.toString()}');
    }
  }

  Future<void> _loadMoreProfiles() async {
    try {
      final authProvider =
          Provider.of<DatabaseAuthProvider>(context, listen: false);
      final currentUserId = authProvider.user?.id;

      if (currentUserId != null) {
        final newProfiles =
            await _discoveryService.getDiscoveryProfiles(currentUserId);
        setState(() {
          _profiles.addAll(newProfiles);
        });

        if (_currentIndex < _profiles.length) {
          _cardController.forward();
        }
      }
    } catch (e) {
      _showError('Failed to load more profiles: ${e.toString()}');
    }
  }

  void _showMatchDialog(ProfileModel profile) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFFF6B9D), Color(0xFFFF8C42)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.favorite,
                color: Colors.white,
                size: 60,
              ),
              const SizedBox(height: 16),
              const Text(
                "It's a Match!",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'You and ${profile.name} liked each other',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.white,
                        side: const BorderSide(color: Colors.white),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('Keep Swiping'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        // Navigate to chat
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: const Color(0xFF2563EB),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('Send Message'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFFFFFFFF), Color(0xFFF9FAFB)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(),
              Expanded(
                child: _isLoading
                    ? _buildLoadingState()
                    : _currentIndex >= _profiles.length
                        ? _buildNoMoreProfilesState()
                        : _buildSwipeArea(),
              ),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withValues(alpha: 0.2),
                ),
                child: const Icon(
                  Icons.favorite,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Discover',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          IconButton(
            onPressed: () {
              // Show filters
            },
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.2),
              ),
              child: const Icon(
                Icons.tune,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Colors.white),
          SizedBox(height: 16),
          Text(
            'Finding amazing people for you...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoMoreProfilesState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.explore_off,
            color: Colors.white,
            size: 80,
          ),
          const SizedBox(height: 16),
          const Text(
            'No more profiles',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Check back later for new people!',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadProfiles,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: const Color(0xFFFF6B9D),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text('Refresh'),
          ),
        ],
      ),
    );
  }

  Widget _buildSwipeArea() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: AnimatedBuilder(
          animation: _cardAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _cardAnimation.value,
              child: Opacity(
                opacity: _cardAnimation.value,
                child: SwipeCard(
                  profile: _profiles[_currentIndex],
                  onSwipe: _handleSwipe,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return AnimatedBuilder(
      animation: _buttonAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _buttonAnimation.value,
          child: ActionButtons(
            onPass: () => _handleSwipe(SwipeDirection.left),
            onSuperLike: () => _handleSwipe(SwipeDirection.up),
            onLike: () => _handleSwipe(SwipeDirection.right),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _cardController.dispose();
    _buttonController.dispose();
    super.dispose();
  }
}
