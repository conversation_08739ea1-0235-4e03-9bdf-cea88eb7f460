import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart';

// Conditional Firebase imports
import 'package:firebase_core/firebase_core.dart'
    if (dart.library.html) 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart' if (dart.library.html) 'firebase_options.dart';

import 'core/providers/database_auth_provider.dart';
import 'core/providers/profile_provider.dart';
import 'features/onboarding/presentation/pages/onboarding_page.dart';
import 'features/auth/presentation/pages/unified_login_page.dart';
import 'features/host/presentation/pages/host_dashboard_page.dart';
import 'features/host/presentation/pages/host_main_app.dart';
import 'demo_app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase only on web platform
  if (kIsWeb) {
    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      debugPrint(
          '✅ Firebase initialized successfully for web - Friendy project: friendy-3d3d8');
    } catch (e) {
      debugPrint('❌ Firebase initialization failed on web: $e');
    }
  } else {
    debugPrint(
        '✅ Friendy app starting on mobile - Firebase disabled for APK compatibility');
  }

  runApp(const FriendyApp());
}

class FriendyApp extends StatelessWidget {
  const FriendyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => DatabaseAuthProvider()),
        ChangeNotifierProvider(create: (_) => ProfileProvider()),
      ],
      child: MaterialApp(
        title: 'Friendy - Dating App',
        theme: ThemeData(
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF2563EB),
            brightness: Brightness.dark, // Changed to dark theme
          ),
          scaffoldBackgroundColor: const Color(0xFF1A1A2E), // Dark background
          textTheme: GoogleFonts.poppinsTextTheme().apply(
            bodyColor: Colors.white, // White text for dark theme
            displayColor: Colors.white,
          ),
          appBarTheme: const AppBarTheme(
            centerTitle: true,
            elevation: 0,
            backgroundColor: Colors.transparent,
          ),
        ),
        home: const AuthWrapper(),
        routes: {
          '/onboarding': (context) => const OnboardingPage(),
          '/auth': (context) => const UnifiedLoginPage(),
          '/home': (context) => const DemoApp(),
          '/host-dashboard': (context) => const HostDashboardPage(),
          '/host-app': (context) => const HostMainApp(),
        },
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<DatabaseAuthProvider>(
      builder: (context, authProvider, child) {
        debugPrint(
            'AuthWrapper - Status: ${authProvider.status}, User: ${authProvider.user?.email}');

        // Show loading during authentication process
        if (authProvider.status == AuthStatus.loading) {
          return const LoadingScreen();
        }

        // If authenticated, show main app
        if (authProvider.status == AuthStatus.authenticated &&
            authProvider.user != null) {
          debugPrint(
              'AuthWrapper - Showing DemoApp for user: ${authProvider.user!.email}');
          return const DemoApp();
        }

        // For all other cases (initial, unauthenticated, error), show onboarding
        debugPrint('AuthWrapper - Showing OnboardingPage');
        return const OnboardingPage();
      },
    );
  }
}

class LoadingScreen extends StatelessWidget {
  const LoadingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFFF6B9D), Color(0xFFFF8C42)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFFF6B9D).withValues(alpha: 0.4),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: const Icon(
                Icons.favorite,
                color: Colors.white,
                size: 50,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Friendy',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 40),
            const CircularProgressIndicator(
              color: Color(0xFFFF6B9D),
            ),
          ],
        ),
      ),
    );
  }
}
