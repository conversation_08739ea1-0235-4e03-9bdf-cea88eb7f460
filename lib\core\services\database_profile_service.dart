import '../config/database_config.dart';

class DatabaseProfileService {
  // Get profile data from database
  static Future<Map<String, dynamic>?> getProfileData(String userId) async {
    try {
      final connection = await DatabaseConfig.getConnection();

      final result = await connection.execute(
        'SELECT * FROM users WHERE id = @userId',
        parameters: {'userId': userId},
      );

      if (result.isNotEmpty) {
        final row = result.first;
        return {
          'id': row[0],
          'firebase_uid': row[1],
          'email': row[2],
          'name': row[3],
          'age': row[5],
          'bio': row[6],
          'location': row[7],
          'profile_images': row[8] ?? [],
          'interests': row[9] ?? [],
          'languages': row[10] ?? [],
          'gender': row[11],
          'looking_for': row[12],
          'relationship_type': row[13],
          'occupation': row[14],
          'education': row[15],
          'height': row[16],
          'is_verified': row[17],
          'coins': row[18],
          'is_premium': row[19],
          'created_at': row[20],
          'updated_at': row[21],
        };
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  // Update profile data in database
  static Future<bool> updateProfile({
    required String userId,
    String? name,
    int? age,
    String? bio,
    String? location,
    String? occupation,
    String? education,
    String? gender,
    String? lookingFor,
    String? relationshipType,
    int? height,
    List<String>? interests,
    List<String>? languages,
  }) async {
    String? query;
    Map<String, dynamic>? parameters;

    try {
      final connection = await DatabaseConfig.getConnection();

      // First, let's check if the user exists
      final userCheck = await connection.execute(
        'SELECT id FROM users WHERE id = @userId',
        parameters: {'userId': userId},
      );

      if (userCheck.isEmpty) {
        print('User not found with ID: $userId');
        return false;
      }

      // Build dynamic update query - only include basic fields first
      List<String> setParts = [];
      parameters = {'userId': userId};

      if (name != null && name.isNotEmpty) {
        setParts.add('name = @name');
        parameters['name'] = name;
      }
      if (age != null) {
        setParts.add('age = @age');
        parameters['age'] = age;
      }
      if (bio != null) {
        setParts.add('bio = @bio');
        parameters['bio'] = bio;
      }
      if (location != null) {
        setParts.add('location = @location');
        parameters['location'] = location;
      }

      // Try to add other fields one by one to see which one fails
      try {
        if (occupation != null) {
          setParts.add('occupation = @occupation');
          parameters['occupation'] = occupation;
        }
      } catch (e) {
        print('Occupation field error: $e');
      }

      try {
        if (education != null) {
          setParts.add('education = @education');
          parameters['education'] = education;
        }
      } catch (e) {
        print('Education field error: $e');
      }

      try {
        if (gender != null) {
          setParts.add('gender = @gender');
          parameters['gender'] = gender;
        }
      } catch (e) {
        print('Gender field error: $e');
      }

      try {
        if (lookingFor != null) {
          setParts.add('looking_for = @lookingFor');
          parameters['lookingFor'] = lookingFor;
        }
      } catch (e) {
        print('LookingFor field error: $e');
      }

      try {
        if (relationshipType != null) {
          setParts.add('relationship_type = @relationshipType');
          parameters['relationshipType'] = relationshipType;
        }
      } catch (e) {
        print('RelationshipType field error: $e');
      }

      try {
        if (height != null) {
          setParts.add('height = @height');
          parameters['height'] = height;
        }
      } catch (e) {
        print('Height field error: $e');
      }

      try {
        if (interests != null) {
          setParts.add('interests = @interests');
          parameters['interests'] = interests;
        }
      } catch (e) {
        print('Interests field error: $e');
      }

      try {
        if (languages != null) {
          setParts.add('languages = @languages');
          parameters['languages'] = languages;
        }
      } catch (e) {
        print('Languages field error: $e');
      }

      if (setParts.isEmpty) {
        print('No fields to update');
        return true;
      }

      // Add updated_at
      setParts.add('updated_at = CURRENT_TIMESTAMP');

      query = '''
        UPDATE users
        SET ${setParts.join(', ')}
        WHERE id = @userId
      ''';

      print('Executing query: $query');
      print('With parameters: $parameters');

      await connection.execute(query, parameters: parameters);
      print('Profile updated successfully');
      return true;
    } catch (e) {
      print('Error updating profile: $e');
      if (query != null) print('Query: $query');
      if (parameters != null) print('Parameters: $parameters');
      return false;
    }
  }

  // Get profile stats (matches, likes, views)
  static Future<Map<String, int>> getProfileStats(String userId) async {
    try {
      final connection = await DatabaseConfig.getConnection();

      // Get matches count
      final matchesResult = await connection.execute(
        'SELECT COUNT(*) FROM matches WHERE (user1_id = @userId OR user2_id = @userId) AND is_active = true',
        parameters: {'userId': userId},
      );

      // Get likes received count
      final likesResult = await connection.execute(
        'SELECT COUNT(*) FROM swipes WHERE swiped_id = @userId AND is_like = true',
        parameters: {'userId': userId},
      );

      // Get profile views (we'll simulate this for now)
      final viewsCount =
          (int.tryParse(matchesResult.first[0].toString()) ?? 0) * 5 +
              (int.tryParse(likesResult.first[0].toString()) ?? 0) * 2;

      return {
        'matches': int.tryParse(matchesResult.first[0].toString()) ?? 0,
        'likes': int.tryParse(likesResult.first[0].toString()) ?? 0,
        'views': viewsCount,
        'super_likes': 0, // We'll implement this later
      };
    } catch (e) {
      return {
        'matches': 0,
        'likes': 0,
        'views': 0,
        'super_likes': 0,
      };
    }
  }

  // Add profile image
  static Future<bool> addProfileImage(String userId, String imageUrl) async {
    try {
      final connection = await DatabaseConfig.getConnection();

      // Get current images
      final result = await connection.execute(
        'SELECT profile_images FROM users WHERE id = @userId',
        parameters: {'userId': userId},
      );

      if (result.isNotEmpty) {
        List<String> currentImages =
            (result.first[0] as List?)?.cast<String>() ?? [];

        // Add new image if not already present
        if (!currentImages.contains(imageUrl)) {
          currentImages.add(imageUrl);

          // Update database
          await connection.execute(
            'UPDATE users SET profile_images = @images, updated_at = CURRENT_TIMESTAMP WHERE id = @userId',
            parameters: {
              'userId': userId,
              'images': currentImages,
            },
          );
        }

        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  // Remove profile image
  static Future<bool> removeProfileImage(String userId, String imageUrl) async {
    try {
      final connection = await DatabaseConfig.getConnection();

      // Get current images
      final result = await connection.execute(
        'SELECT profile_images FROM users WHERE id = @userId',
        parameters: {'userId': userId},
      );

      if (result.isNotEmpty) {
        List<String> currentImages =
            (result.first[0] as List?)?.cast<String>() ?? [];

        // Remove image
        currentImages.remove(imageUrl);

        // Update database
        await connection.execute(
          'UPDATE users SET profile_images = @images, updated_at = CURRENT_TIMESTAMP WHERE id = @userId',
          parameters: {
            'userId': userId,
            'images': currentImages,
          },
        );

        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  // Calculate profile completion percentage
  static double calculateCompletionPercentage(
      Map<String, dynamic> profileData) {
    int completedFields = 0;
    int totalFields = 10; // Adjust based on required fields

    if (profileData['name']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['age'] != null) completedFields++;
    if (profileData['bio']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['location']?.toString().isNotEmpty == true) {
      completedFields++;
    }
    if (profileData['occupation']?.toString().isNotEmpty == true) {
      completedFields++;
    }
    if (profileData['education']?.toString().isNotEmpty == true) {
      completedFields++;
    }
    if (profileData['gender']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['looking_for']?.toString().isNotEmpty == true) {
      completedFields++;
    }
    if ((profileData['interests'] as List?)?.isNotEmpty == true) {
      completedFields++;
    }
    if ((profileData['profile_images'] as List?)?.isNotEmpty == true) {
      completedFields++;
    }

    return (completedFields / totalFields) * 100;
  }

  // Test function to verify profile update is working
  static Future<void> testProfileUpdate() async {
    print('Testing profile update...');

    // Test with a known user ID
    const testUserId = '55f2a97c-caae-4866-8bb7-f902a38bb29e';

    final success = await updateProfile(
      userId: testUserId,
      name: 'Test User Updated',
      bio: 'This is a test bio update',
      location: 'Test City',
    );

    print('Profile update test result: $success');

    if (success) {
      // Verify the update worked
      final profileData = await getProfileData(testUserId);
      print('Updated profile data: $profileData');
    }
  }
}
