import 'package:shared_preferences/shared_preferences.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:convert';
import 'dart:math';

class ProfileService {
  static const String _profileDataKey = 'profile_data';
  static const String _profileImagesKey = 'profile_images';
  static const String _profileStatsKey = 'profile_stats';
  static const String _profileSettingsKey = 'profile_settings';

  // Backend sync flag
  static const String _syncEnabledKey = 'backend_sync_enabled';

  // Check if backend sync is enabled
  Future<bool> isBackendSyncEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_syncEnabledKey) ?? true; // Default to true
  }

  // Enable/disable backend sync
  Future<void> setBackendSync(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_syncEnabledKey, enabled);
  }

  // Get current profile data
  Future<Map<String, dynamic>?> getProfileData() async {
    final prefs = await SharedPreferences.getInstance();
    final profileJson = prefs.getString(_profileDataKey);
    if (profileJson != null) {
      return json.decode(profileJson);
    }
    return null;
  }

  // Save profile data
  Future<bool> saveProfileData(Map<String, dynamic> profileData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      profileData['updated_at'] = DateTime.now().toIso8601String();
      await prefs.setString(_profileDataKey, json.encode(profileData));
      return true;
    } catch (e) {
      return false;
    }
  }

  // Update specific profile fields
  Future<bool> updateProfile({
    String? name,
    int? age,
    String? bio,
    String? location,
    String? occupation,
    String? education,
    List<String>? interests,
    String? gender,
    String? lookingFor,
    int? height,
    List<String>? languages,
    String? relationshipType,
    String? userId,
  }) async {
    try {
      final currentProfile = await getProfileData() ?? {};

      if (name != null) currentProfile['name'] = name;
      if (age != null) currentProfile['age'] = age;
      if (bio != null) currentProfile['bio'] = bio;
      if (location != null) currentProfile['location'] = location;
      if (occupation != null) currentProfile['occupation'] = occupation;
      if (education != null) currentProfile['education'] = education;
      if (interests != null) currentProfile['interests'] = interests;
      if (gender != null) currentProfile['gender'] = gender;
      if (lookingFor != null) {
        currentProfile['looking_for'] = lookingFor;
      }
      if (height != null) currentProfile['height'] = height;
      if (languages != null) currentProfile['languages'] = languages;
      if (relationshipType != null) {
        currentProfile['relationship_type'] = relationshipType;
      }

      // Save to local storage first
      final localSuccess = await saveProfileData(currentProfile);

      // Try to sync with backend if enabled and userId is provided
      if (localSuccess && await isBackendSyncEnabled() && userId != null) {
        try {
          await _syncProfileToBackend(
            userId: userId,
            name: name,
            age: age,
            bio: bio,
            location: location,
            interests: interests,
            gender: gender,
            lookingFor: lookingFor,
          );
        } catch (e) {
          // Backend sync failed, but local save succeeded
          print('Backend sync failed: $e');
        }
      }

      return localSuccess;
    } catch (e) {
      return false;
    }
  }

  // Sync profile data to backend
  Future<bool> _syncProfileToBackend({
    required String userId,
    String? name,
    int? age,
    String? bio,
    String? location,
    List<String>? interests,
    String? gender,
    String? lookingFor,
  }) async {
    try {
      // For now, just return true as we're using the database service
      return true;
    } catch (e) {
      print('Backend sync error: $e');
      return false;
    }
  }

  // Profile Images Management
  Future<List<String>> getProfileImages() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_profileImagesKey) ?? [];
  }

  Future<bool> addProfileImage(XFile imageFile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentImages = await getProfileImages();

      // Limit to 6 images
      if (currentImages.length >= 6) {
        return false;
      }

      // In a real app, upload to Firebase Storage
      // For demo, we'll store the file path
      final imagePath = imageFile.path;
      currentImages.add(imagePath);

      await prefs.setStringList(_profileImagesKey, currentImages);

      // Update profile data with new image
      final profileData = await getProfileData() ?? {};
      profileData['profile_images'] = currentImages;
      await saveProfileData(profileData);

      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> removeProfileImage(String imagePath) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentImages = await getProfileImages();
      currentImages.remove(imagePath);

      await prefs.setStringList(_profileImagesKey, currentImages);

      // Update profile data
      final profileData = await getProfileData() ?? {};
      profileData['profile_images'] = currentImages;
      await saveProfileData(profileData);

      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> reorderProfileImages(List<String> newOrder) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_profileImagesKey, newOrder);

      // Update profile data
      final profileData = await getProfileData() ?? {};
      profileData['profile_images'] = newOrder;
      await saveProfileData(profileData);

      return true;
    } catch (e) {
      return false;
    }
  }

  // Profile Statistics
  Future<Map<String, int>> getProfileStats() async {
    final prefs = await SharedPreferences.getInstance();
    final statsJson = prefs.getString(_profileStatsKey);
    if (statsJson != null) {
      final stats = json.decode(statsJson);
      return Map<String, int>.from(stats);
    }

    // Return default stats if none exist
    return {
      'matches': Random().nextInt(50) + 10,
      'likes': Random().nextInt(200) + 50,
      'views': Random().nextInt(500) + 100,
      'super_likes': Random().nextInt(20) + 5,
    };
  }

  Future<bool> updateProfileStats(Map<String, int> stats) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_profileStatsKey, json.encode(stats));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> incrementStat(String statName, {int increment = 1}) async {
    try {
      final currentStats = await getProfileStats();
      currentStats[statName] = (currentStats[statName] ?? 0) + increment;
      return await updateProfileStats(currentStats);
    } catch (e) {
      return false;
    }
  }

  // Profile Settings
  Future<Map<String, dynamic>> getProfileSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final settingsJson = prefs.getString(_profileSettingsKey);
    if (settingsJson != null) {
      return json.decode(settingsJson);
    }

    // Return default settings
    return {
      'show_online_status': true,
      'show_distance': true,
      'show_age': true,
      'push_notifications': true,
      'email_notifications': false,
      'max_distance': 50.0,
      'age_range_min': 18,
      'age_range_max': 35,
      'discovery_enabled': true,
      'show_me_on_friendy': true,
    };
  }

  Future<bool> updateProfileSettings(Map<String, dynamic> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_profileSettingsKey, json.encode(settings));
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> updateSetting(String key, dynamic value) async {
    try {
      final currentSettings = await getProfileSettings();
      currentSettings[key] = value;
      return await updateProfileSettings(currentSettings);
    } catch (e) {
      return false;
    }
  }

  // Profile Completion
  Future<double> getProfileCompletionPercentage() async {
    final profileData = await getProfileData();
    if (profileData == null) return 0.0;

    int completedFields = 0;
    int totalFields = 12; // Total important fields

    // Check required fields
    if (profileData['name']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['age'] != null) completedFields++;
    if (profileData['bio']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['location']?.toString().isNotEmpty == true) {
      completedFields++;
    }
    if (profileData['gender']?.toString().isNotEmpty == true) completedFields++;
    if (profileData['looking_for']?.toString().isNotEmpty == true) {
      completedFields++;
    }
    if (profileData['occupation']?.toString().isNotEmpty == true) {
      completedFields++;
    }
    if (profileData['education']?.toString().isNotEmpty == true) {
      completedFields++;
    }
    if (profileData['interests']?.isNotEmpty == true) completedFields++;
    if (profileData['profile_images']?.isNotEmpty == true) completedFields++;
    if (profileData['height'] != null) completedFields++;
    if (profileData['languages']?.isNotEmpty == true) completedFields++;

    return (completedFields / totalFields) * 100;
  }

  // Profile Validation
  Future<List<String>> getProfileValidationErrors() async {
    final profileData = await getProfileData();
    final errors = <String>[];

    if (profileData == null) {
      errors.add('Profile data not found');
      return errors;
    }

    if (profileData['name']?.toString().isEmpty != false) {
      errors.add('Name is required');
    }

    if (profileData['age'] == null) {
      errors.add('Age is required');
    }

    if (profileData['gender']?.toString().isEmpty != false) {
      errors.add('Gender is required');
    }

    if (profileData['profile_images']?.isEmpty != false) {
      errors.add('At least one photo is required');
    }

    if (profileData['bio']?.toString().isEmpty != false) {
      errors.add('Bio helps others know you better');
    }

    if (profileData['interests']?.isEmpty != false) {
      errors.add('Add interests to find better matches');
    }

    return errors;
  }

  // Clear all profile data
  Future<bool> clearProfileData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_profileDataKey);
      await prefs.remove(_profileImagesKey);
      await prefs.remove(_profileStatsKey);
      await prefs.remove(_profileSettingsKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Initialize default profile for new users
  Future<bool> initializeDefaultProfile(
      String userId, String name, String email) async {
    final defaultProfile = {
      'id': userId,
      'name': name,
      'email': email,
      'age': null,
      'bio': '',
      'location': '',
      'occupation': '',
      'education': '',
      'gender': '',
      'looking_for': '',
      'height': null,
      'interests': <String>[],
      'languages': <String>[],
      'relationship_type': '',
      'profile_images': <String>[],
      'is_verified': false,
      'is_premium': false,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };

    return await saveProfileData(defaultProfile);
  }
}
