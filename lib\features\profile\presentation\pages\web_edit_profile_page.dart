import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../core/providers/database_auth_provider.dart';
import '../../../../core/services/web_profile_service.dart';

class WebEditProfilePage extends StatefulWidget {
  const WebEditProfilePage({super.key});

  @override
  State<WebEditProfilePage> createState() => _WebEditProfilePageState();
}

class _WebEditProfilePageState extends State<WebEditProfilePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _bioController = TextEditingController();
  final _locationController = TextEditingController();
  final _occupationController = TextEditingController();
  final _educationController = TextEditingController();

  String? _selectedGender;
  String? _selectedLookingFor;
  String? _selectedRelationshipType;
  int? _selectedAge;
  int? _selectedHeight;
  List<String> _selectedInterests = [];
  List<String> _selectedLanguages = [];

  bool _isLoading = false;
  bool _isSaving = false;
  Map<String, dynamic>? _profileData;

  final List<String> _genderOptions = ['Male', 'Female', 'Non-binary', 'Other'];
  final List<String> _lookingForOptions = ['Men', 'Women', 'Everyone'];
  final List<String> _relationshipTypeOptions = [
    'Single',
    'In a relationship',
    'Married',
    'Divorced',
    'Widowed',
    'It\'s complicated',
  ];
  final List<String> _interestOptions = [
    'Travel',
    'Photography',
    'Music',
    'Sports',
    'Fitness',
    'Cooking',
    'Reading',
    'Movies',
    'Art',
    'Dancing',
    'Gaming',
    'Technology',
    'Fashion',
    'Food',
    'Nature',
    'Pets',
    'Yoga',
    'Meditation',
    'Writing'
  ];
  final List<String> _languageOptions = [
    'English',
    'Spanish',
    'French',
    'German',
    'Italian',
    'Portuguese',
    'Russian',
    'Chinese',
    'Japanese',
    'Korean',
    'Arabic',
    'Hindi'
  ];

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _bioController.dispose();
    _locationController.dispose();
    _occupationController.dispose();
    _educationController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    final authProvider = context.read<DatabaseAuthProvider>();
    final user = authProvider.user;

    if (user?.id != null) {
      final profileData = await WebProfileService.getProfileData(user!.id);

      if (mounted && profileData != null) {
        setState(() {
          _profileData = profileData;
          _nameController.text = profileData['name']?.toString() ?? '';
          _bioController.text = profileData['bio']?.toString() ?? '';
          _locationController.text = profileData['location']?.toString() ?? '';
          _occupationController.text =
              profileData['occupation']?.toString() ?? '';
          _educationController.text =
              profileData['education']?.toString() ?? '';
          _selectedGender = profileData['gender']?.toString().isNotEmpty == true
              ? profileData['gender']
              : null;
          _selectedLookingFor =
              profileData['looking_for']?.toString().isNotEmpty == true
                  ? profileData['looking_for']
                  : null;
          _selectedRelationshipType =
              profileData['relationship_type']?.toString().isNotEmpty == true
                  ? profileData['relationship_type']
                  : null;
          _selectedAge = profileData['age'];
          _selectedHeight = profileData['height'];
          _selectedInterests =
              List<String>.from(profileData['interests'] ?? []);
          _selectedLanguages =
              List<String>.from(profileData['languages'] ?? []);
        });
      }
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSaving = true;
    });

    final authProvider = context.read<DatabaseAuthProvider>();
    final user = authProvider.user;

    if (user?.id != null) {
      final success = await WebProfileService.updateProfile(
        userId: user!.id,
        name: _nameController.text.trim(),
        age: _selectedAge,
        bio: _bioController.text.trim(),
        location: _locationController.text.trim(),
        occupation: _occupationController.text.trim(),
        education: _educationController.text.trim(),
        gender: _selectedGender,
        lookingFor: _selectedLookingFor,
        relationshipType: _selectedRelationshipType,
        height: _selectedHeight,
        interests: _selectedInterests,
        languages: _selectedLanguages,
      );

      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profile updated successfully!'),
              backgroundColor: Color(0xFFFF6B9D),
            ),
          );
          Navigator.pop(context, true); // Return true to indicate success
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to update profile'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF1A1A1A),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Edit Profile',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _isSaving ? null : _saveProfile,
            child: _isSaving
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(Color(0xFFFF6B9D)),
                    ),
                  )
                : const Text(
                    'Save',
                    style: TextStyle(
                      color: Color(0xFFFF6B9D),
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFFFF6B9D)),
            )
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Basic Information
                    _buildSection(
                      title: 'Basic Information',
                      children: [
                        _buildTextField(
                          controller: _nameController,
                          label: 'Name',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Name is required';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        _buildDropdown(
                          label: 'Gender',
                          value: _selectedGender,
                          items: _genderOptions,
                          onChanged: (value) {
                            setState(() {
                              _selectedGender = value;
                            });
                          },
                        ),
                        const SizedBox(height: 16),
                        _buildDropdown(
                          label: 'Looking For',
                          value: _selectedLookingFor,
                          items: _lookingForOptions,
                          onChanged: (value) {
                            setState(() {
                              _selectedLookingFor = value;
                            });
                          },
                        ),
                        const SizedBox(height: 16),
                        _buildAgeSelector(),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // About Me
                    _buildSection(
                      title: 'About Me',
                      children: [
                        _buildTextField(
                          controller: _bioController,
                          label: 'Bio',
                          maxLines: 4,
                          hint: 'Tell others about yourself...',
                        ),
                        const SizedBox(height: 16),
                        _buildTextField(
                          controller: _locationController,
                          label: 'Location',
                          hint: 'City, Country',
                        ),
                        const SizedBox(height: 16),
                        _buildTextField(
                          controller: _occupationController,
                          label: 'Occupation',
                          hint: 'Your job title',
                        ),
                        const SizedBox(height: 16),
                        _buildTextField(
                          controller: _educationController,
                          label: 'Education',
                          hint: 'Your education background',
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Interests
                    _buildSection(
                      title: 'Interests',
                      children: [
                        _buildInterestSelector(),
                      ],
                    ),

                    const SizedBox(height: 32),

                    // Save Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isSaving ? null : _saveProfile,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFFF6B9D),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: _isSaving
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : const Text(
                                'Save Changes',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSection(
      {required String title, required List<Widget> children}) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A2A),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      validator: validator,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        labelStyle: const TextStyle(color: Colors.grey),
        hintStyle: const TextStyle(color: Colors.grey),
        filled: true,
        fillColor: const Color(0xFF3A3A3A),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFFF6B9D)),
        ),
      ),
    );
  }

  Widget _buildDropdown({
    required String label,
    required String? value,
    required List<String> items,
    required void Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      onChanged: onChanged,
      style: const TextStyle(color: Colors.white),
      dropdownColor: const Color(0xFF3A3A3A),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: const TextStyle(color: Colors.grey),
        filled: true,
        fillColor: const Color(0xFF3A3A3A),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFFF6B9D)),
        ),
      ),
      items: items.map((String item) {
        return DropdownMenuItem<String>(
          value: item,
          child: Text(item),
        );
      }).toList(),
    );
  }

  Widget _buildAgeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Age',
          style: TextStyle(color: Colors.grey, fontSize: 16),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: const Color(0xFF3A3A3A),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _selectedAge?.toString() ?? 'Select age',
                style: TextStyle(
                  color: _selectedAge != null ? Colors.white : Colors.grey,
                  fontSize: 16,
                ),
              ),
              GestureDetector(
                onTap: () => _showAgePicker(),
                child:
                    const Icon(Icons.keyboard_arrow_down, color: Colors.grey),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInterestSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select your interests (up to 10)',
          style: TextStyle(color: Colors.grey, fontSize: 14),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _interestOptions.map((interest) {
            final isSelected = _selectedInterests.contains(interest);
            return GestureDetector(
              onTap: () {
                setState(() {
                  if (isSelected) {
                    _selectedInterests.remove(interest);
                  } else if (_selectedInterests.length < 10) {
                    _selectedInterests.add(interest);
                  }
                });
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color(0xFFFF6B9D)
                      : const Color(0xFF3A3A3A),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected
                        ? const Color(0xFFFF6B9D)
                        : Colors.grey.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  interest,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey,
                    fontSize: 14,
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  void _showAgePicker() {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF2A2A2A),
      builder: (context) {
        return Container(
          height: 300,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const Text(
                'Select Age',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: 65, // Ages 18-82
                  itemBuilder: (context, index) {
                    final age = index + 18;
                    return ListTile(
                      title: Text(
                        '$age years old',
                        style: const TextStyle(color: Colors.white),
                      ),
                      onTap: () {
                        setState(() {
                          _selectedAge = age;
                        });
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
