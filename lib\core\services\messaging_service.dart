import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';

class MessagingService {
  static final MessagingService _instance = MessagingService._internal();
  factory MessagingService() => _instance;

  // Mock data for mobile
  final List<Map<String, dynamic>> _mockConversations = [];
  final Map<String, List<Map<String, dynamic>>> _mockMessages = {};
  final StreamController<List<Map<String, dynamic>>> _conversationsController =
      StreamController<List<Map<String, dynamic>>>.broadcast();
  final Map<String, StreamController<List<Map<String, dynamic>>>>
      _messageControllers = {};

  MessagingService._internal() {
    _initializeMockData();
  }

  void _initializeMockData() {
    // Initialize with demo conversations
    _mockConversations.addAll([
      {
        'id': 'conv_1',
        'otherUserId': 'user_1',
        'otherUserName': '<PERSON>',
        'otherUserAvatar':
            'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',
        'lastMessage': 'Hey! How are you doing? 😊',
        'lastMessageAt': DateTime.now().subtract(const Duration(minutes: 5)),
        'lastMessageSenderId': 'user_1',
        'unreadCount': 2,
        'isOnline': true,
      },
      {
        'id': 'conv_2',
        'otherUserId': 'user_2',
        'otherUserName': 'Sarah Johnson',
        'otherUserAvatar':
            'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=face',
        'lastMessage': 'That sounds great! Can\'t wait 💕',
        'lastMessageAt': DateTime.now().subtract(const Duration(minutes: 30)),
        'lastMessageSenderId': 'current_user',
        'unreadCount': 0,
        'isOnline': true,
      },
      {
        'id': 'conv_3',
        'otherUserId': 'user_3',
        'otherUserName': 'Jessica Brown',
        'otherUserAvatar':
            'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=150&h=150&fit=crop&crop=face',
        'lastMessage': 'See you tomorrow! 🌟',
        'lastMessageAt': DateTime.now().subtract(const Duration(hours: 2)),
        'lastMessageSenderId': 'user_3',
        'unreadCount': 1,
        'isOnline': false,
      },
      {
        'id': 'conv_4',
        'otherUserId': 'user_4',
        'otherUserName': 'Maya Patel',
        'otherUserAvatar':
            'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=150&h=150&fit=crop&crop=face',
        'lastMessage': 'Thanks for the lovely evening! ✨',
        'lastMessageAt': DateTime.now().subtract(const Duration(hours: 6)),
        'lastMessageSenderId': 'user_4',
        'unreadCount': 0,
        'isOnline': true,
      },
      {
        'id': 'conv_5',
        'otherUserId': 'user_5',
        'otherUserName': 'Sophie Chen',
        'otherUserAvatar':
            'https://images.unsplash.com/photo-1488426862026-3ee34a7d66df?w=150&h=150&fit=crop&crop=face',
        'lastMessage': 'Good morning! Hope you have a great day 🌸',
        'lastMessageAt': DateTime.now().subtract(const Duration(hours: 12)),
        'lastMessageSenderId': 'user_5',
        'unreadCount': 3,
        'isOnline': false,
      },
      {
        'id': 'conv_6',
        'otherUserId': 'user_6',
        'otherUserName': 'Aria Rodriguez',
        'otherUserAvatar':
            'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
        'lastMessage': 'You: Looking forward to our date! 💖',
        'lastMessageAt': DateTime.now().subtract(const Duration(days: 1)),
        'lastMessageSenderId': 'current_user',
        'unreadCount': 0,
        'isOnline': true,
      },
      {
        'id': 'conv_7',
        'otherUserId': 'user_7',
        'otherUserName': 'Lily Thompson',
        'otherUserAvatar':
            'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        'lastMessage': 'That movie was amazing! 🎬',
        'lastMessageAt': DateTime.now().subtract(const Duration(days: 2)),
        'lastMessageSenderId': 'user_7',
        'unreadCount': 0,
        'isOnline': false,
      },
    ]);

    // Initialize mock messages
    _mockMessages['conv_1'] = [
      {
        'id': 'msg_1',
        'senderId': 'user_1',
        'content':
            'Hi there! 👋 I saw your profile and thought we might have a lot in common!',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(hours: 2)),
        'readBy': ['user_1'],
      },
      {
        'id': 'msg_2',
        'senderId': 'current_user',
        'content':
            'Hello Emma! Nice to meet you! 😊 I love your photos, especially the one at the beach!',
        'type': 'text',
        'sentAt':
            DateTime.now().subtract(const Duration(hours: 1, minutes: 45)),
        'readBy': ['current_user', 'user_1'],
      },
      {
        'id': 'msg_3',
        'senderId': 'user_1',
        'content':
            'Aww thank you! That was from my trip to Bali last month 🏝️',
        'type': 'text',
        'sentAt':
            DateTime.now().subtract(const Duration(hours: 1, minutes: 30)),
        'readBy': ['user_1'],
      },
      {
        'id': 'msg_4',
        'senderId': 'current_user',
        'content':
            'Wow, Bali looks amazing! I\'ve always wanted to go there. How was it?',
        'type': 'text',
        'sentAt':
            DateTime.now().subtract(const Duration(hours: 1, minutes: 15)),
        'readBy': ['current_user', 'user_1'],
      },
      {
        'id': 'msg_5',
        'senderId': 'user_1',
        'content': 'Hey! How are you doing? 😊',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(minutes: 5)),
        'readBy': ['user_1'],
      },
    ];

    _mockMessages['conv_2'] = [
      {
        'id': 'msg_6',
        'senderId': 'user_2',
        'content': 'Hey! I really enjoyed our conversation yesterday 💕',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(hours: 3)),
        'readBy': ['user_2', 'current_user'],
      },
      {
        'id': 'msg_7',
        'senderId': 'user_2',
        'content': 'Would you like to grab coffee sometime this weekend?',
        'type': 'text',
        'sentAt':
            DateTime.now().subtract(const Duration(hours: 2, minutes: 45)),
        'readBy': ['user_2', 'current_user'],
      },
      {
        'id': 'msg_8',
        'senderId': 'current_user',
        'content': 'That sounds great! Can\'t wait 💕',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(minutes: 30)),
        'readBy': ['current_user', 'user_2'],
      },
    ];

    _mockMessages['conv_3'] = [
      {
        'id': 'msg_9',
        'senderId': 'user_3',
        'content': 'Thanks for such a wonderful evening! 🌟',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(hours: 3)),
        'readBy': ['user_3'],
      },
      {
        'id': 'msg_10',
        'senderId': 'current_user',
        'content':
            'I had an amazing time too! You\'re so much fun to be around 😊',
        'type': 'text',
        'sentAt':
            DateTime.now().subtract(const Duration(hours: 2, minutes: 30)),
        'readBy': ['current_user', 'user_3'],
      },
      {
        'id': 'msg_11',
        'senderId': 'user_3',
        'content': 'See you tomorrow! 🌟',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(hours: 2)),
        'readBy': ['user_3'],
      },
    ];

    // Add more conversations with messages
    _mockMessages['conv_4'] = [
      {
        'id': 'msg_12',
        'senderId': 'user_4',
        'content': 'Thanks for the lovely evening! ✨',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(hours: 6)),
        'readBy': ['user_4', 'current_user'],
      },
    ];

    _mockMessages['conv_5'] = [
      {
        'id': 'msg_13',
        'senderId': 'user_5',
        'content': 'Good morning! Hope you have a great day 🌸',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(hours: 12)),
        'readBy': ['user_5'],
      },
      {
        'id': 'msg_14',
        'senderId': 'user_5',
        'content': 'Did you see the sunset yesterday? It was breathtaking! 🌅',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(hours: 11)),
        'readBy': ['user_5'],
      },
      {
        'id': 'msg_15',
        'senderId': 'user_5',
        'content': 'I was thinking about you 💭',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(hours: 10)),
        'readBy': ['user_5'],
      },
    ];

    _mockMessages['conv_6'] = [
      {
        'id': 'msg_16',
        'senderId': 'current_user',
        'content': 'Looking forward to our date! 💖',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(days: 1)),
        'readBy': ['current_user', 'user_6'],
      },
    ];

    _mockMessages['conv_7'] = [
      {
        'id': 'msg_17',
        'senderId': 'user_7',
        'content': 'That movie was amazing! 🎬',
        'type': 'text',
        'sentAt': DateTime.now().subtract(const Duration(days: 2)),
        'readBy': ['user_7', 'current_user'],
      },
    ];
  }

  // Get current user ID
  String? get currentUserId => 'current_user'; // Mock user ID for mobile

  // Create or get conversation between two users
  Future<String> createOrGetConversation(String otherUserId) async {
    if (currentUserId == null) throw Exception('User not authenticated');

    final participants = [currentUserId!, otherUserId]..sort();
    final conversationId = participants.join('_');

    // Check if conversation exists in mock data
    final existingConv = _mockConversations.firstWhere(
      (conv) => conv['id'] == conversationId,
      orElse: () => {},
    );

    if (existingConv.isEmpty) {
      // Create new mock conversation
      _mockConversations.add({
        'id': conversationId,
        'otherUserId': otherUserId,
        'otherUserName': 'User $otherUserId',
        'otherUserAvatar':
            'https://via.placeholder.com/150/FF6B9D/FFFFFF?text=U',
        'lastMessage': null,
        'lastMessageAt': null,
        'lastMessageSenderId': null,
        'unreadCount': 0,
      });
      _mockMessages[conversationId] = [];
    }

    return conversationId;
  }

  // Send a text message
  Future<void> sendMessage({
    required String conversationId,
    required String content,
    String type = 'text',
  }) async {
    if (currentUserId == null) throw Exception('User not authenticated');

    // Add to mock messages
    final messageId = 'msg_${DateTime.now().millisecondsSinceEpoch}';
    final messageData = {
      'id': messageId,
      'senderId': currentUserId!,
      'content': content,
      'type': type,
      'sentAt': DateTime.now(),
      'readBy': [currentUserId!],
    };

    // Add message to conversation
    if (_mockMessages[conversationId] == null) {
      _mockMessages[conversationId] = [];
    }
    _mockMessages[conversationId]!.add(messageData);

    // Update conversation last message
    final convIndex =
        _mockConversations.indexWhere((conv) => conv['id'] == conversationId);
    if (convIndex != -1) {
      _mockConversations[convIndex]['lastMessage'] = content;
      _mockConversations[convIndex]['lastMessageAt'] = DateTime.now();
      _mockConversations[convIndex]['lastMessageSenderId'] = currentUserId!;
    }

    // Notify listeners
    if (_messageControllers[conversationId] != null) {
      _messageControllers[conversationId]!.add(_mockMessages[conversationId]!);
    }
    _conversationsController.add(List.from(_mockConversations));
  }

  // Send image message (mock implementation)
  Future<void> sendImageMessage({
    required String conversationId,
    required XFile imageFile,
  }) async {
    // For demo purposes, just send a text message indicating an image
    await sendMessage(
      conversationId: conversationId,
      content: '📷 Image sent',
      type: 'image',
    );
  }

  // Get messages stream for a conversation
  Stream<List<Map<String, dynamic>>> getMessagesStream(String conversationId) {
    if (_messageControllers[conversationId] == null) {
      _messageControllers[conversationId] =
          StreamController<List<Map<String, dynamic>>>.broadcast();
    }

    // Emit initial data
    final messages = _mockMessages[conversationId] ?? [];
    Future.microtask(() {
      _messageControllers[conversationId]!.add(messages);
    });

    return _messageControllers[conversationId]!.stream;
  }

  // Get conversations stream for current user
  Stream<List<Map<String, dynamic>>> getConversationsStream() {
    if (currentUserId == null) return Stream.value([]);

    // Emit initial data
    Future.microtask(() {
      _conversationsController.add(List.from(_mockConversations));
    });

    return _conversationsController.stream;
  }

  // Mark messages as read (mock implementation)
  Future<void> markMessagesAsRead(String conversationId) async {
    // Update unread count
    final convIndex =
        _mockConversations.indexWhere((conv) => conv['id'] == conversationId);
    if (convIndex != -1) {
      _mockConversations[convIndex]['unreadCount'] = 0;
      _conversationsController.add(List.from(_mockConversations));
    }
  }

  // Delete a message (mock implementation)
  Future<void> deleteMessage(String conversationId, String messageId) async {
    final messages = _mockMessages[conversationId];
    if (messages != null) {
      messages.removeWhere((msg) => msg['id'] == messageId);
      if (_messageControllers[conversationId] != null) {
        _messageControllers[conversationId]!.add(messages);
      }
    }
  }

  // Block/Unblock user (mock implementation)
  Future<void> blockUser(String userId) async {
    debugPrint('User $userId blocked (demo)');
  }

  Future<void> unblockUser(String userId) async {
    debugPrint('User $userId unblocked (demo)');
  }

  // Check if user is blocked (mock implementation)
  Future<bool> isUserBlocked(String userId) async {
    return false; // No users blocked in demo
  }

  // Report user (mock implementation)
  Future<void> reportUser({
    required String userId,
    required String reason,
    String? description,
  }) async {
    debugPrint('User $userId reported for: $reason (demo)');
  }

  // Dispose resources
  void dispose() {
    _conversationsController.close();
    for (final controller in _messageControllers.values) {
      controller.close();
    }
  }
}
