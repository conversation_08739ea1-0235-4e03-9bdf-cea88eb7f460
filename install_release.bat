@echo off
echo ========================================
echo    Friendy Release APK Installation
echo ========================================
echo.

echo [1/3] Checking for release APK...
set APK_PATH=build\app\outputs\flutter-apk\app-release.apk

if exist "%APK_PATH%" (
    echo ✅ Release APK found: %APK_PATH%
    for %%A in ("%APK_PATH%") do echo 📱 APK Size: %%~zA bytes
) else (
    echo ❌ Release APK not found. Building now...
    echo Building release APK...
    flutter build apk --release
    if errorlevel 1 (
        echo ❌ Build failed!
        pause
        exit /b 1
    )
)

echo.
echo [2/3] Checking device connection...
adb devices
if errorlevel 1 (
    echo ❌ ADB not found or device not connected!
    echo Please ensure:
    echo - Android device is connected via USB
    echo - USB debugging is enabled
    echo - Device is authorized for debugging
    pause
    exit /b 1
)

echo.
echo [3/3] Installing release APK...
echo Installing to device...
adb install -r "%APK_PATH%"
if errorlevel 1 (
    echo ❌ Installation failed!
    echo.
    echo Trying to uninstall previous version first...
    adb uninstall com.friendy.app
    echo Retrying installation...
    adb install "%APK_PATH%"
    if errorlevel 1 (
        echo ❌ Installation still failed!
        echo.
        echo Possible solutions:
        echo 1. Enable "Install unknown apps" in device settings
        echo 2. Check if device has enough storage space
        echo 3. Try installing manually by copying APK to device
        pause
        exit /b 1
    )
)

echo.
echo ✅ SUCCESS: Friendy release APK installed successfully!
echo 🚀 You can now launch the app from your device
echo.
pause
