// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAmLDpzIQhPBMgOCLit060mkHYRE1bCsVM',
    appId: '1:886538986078:web:friendy-web-app',
    messagingSenderId: '886538986078',
    projectId: 'friendy-3d3d8',
    authDomain: 'friendy-3d3d8.firebaseapp.com',
    storageBucket: 'friendy-3d3d8.firebasestorage.app',
    measurementId: 'G-MEASUREMENT123',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAHowpSbz1h-PPssGo53G0ULCjlauuZdtg',
    appId: '1:886538986078:android:813549fef7f168d5ce0acf',
    messagingSenderId: '886538986078',
    projectId: 'friendy-3d3d8',
    storageBucket: 'friendy-3d3d8.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBYmvuJ5_5wX8QZ9X8QZ9X8QZ9X8QZ9X8Q',
    appId: '1:123456789:ios:abcdef123456789',
    messagingSenderId: '123456789',
    projectId: 'friendy-3d3d8',
    storageBucket: 'friendy-3d3d8.appspot.com',
    iosBundleId: 'com.friendy.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBYmvuJ5_5wX8QZ9X8QZ9X8QZ9X8QZ9X8Q',
    appId: '1:123456789:macos:abcdef123456789',
    messagingSenderId: '123456789',
    projectId: 'friendy-3d3d8',
    storageBucket: 'friendy-3d3d8.appspot.com',
    iosBundleId: 'com.friendy.app',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBYmvuJ5_5wX8QZ9X8QZ9X8QZ9X8QZ9X8Q',
    appId: '1:123456789:windows:abcdef123456789',
    messagingSenderId: '123456789',
    projectId: 'friendy-3d3d8',
    authDomain: 'friendy-3d3d8.firebaseapp.com',
    storageBucket: 'friendy-3d3d8.appspot.com',
    measurementId: 'G-XXXXXXXXXX',
  );
}
